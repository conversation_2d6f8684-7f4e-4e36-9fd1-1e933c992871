/*
  # Set Super Admin User

  1. Updates
    - Find user with email '<EMAIL>' and set their profile to super_admin
    - Grant all permissions (unlimited offers, analytics, ecommerce, background changes)

  2. Security
    - This is a one-time administrative action
    - Super admin will have full access to all features and admin dashboard
*/

-- Update the user <NAME_EMAIL> to be super admin
UPDATE user_profiles 
SET 
  user_type = 'super_admin',
  max_offers = -1,
  has_analytics = true,
  has_ecommerce = true,
  can_change_background = true,
  updated_at = now()
WHERE user_id = (
  SELECT id 
  FROM auth.users 
  WHERE email = '<EMAIL>'
);

-- If the user doesn't have a profile yet, create one
INSERT INTO user_profiles (
  user_id,
  user_type,
  subscription_status,
  max_offers,
  has_analytics,
  has_ecommerce,
  can_change_background
)
SELECT 
  id,
  'super_admin',
  'active',
  -1,
  true,
  true,
  true
FROM auth.users 
WHERE email = '<EMAIL>'
  AND id NOT IN (SELECT user_id FROM user_profiles)
ON CONFLICT (user_id) DO NOTHING;