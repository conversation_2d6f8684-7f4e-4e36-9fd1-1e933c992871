/*
  # Fix infinite recursion in user_profiles RLS policies

  1. Problem
    - Current policies for super admin access create infinite recursion
    - Policies reference user_profiles table within their own conditions
    - This causes circular dependency when checking permissions

  2. Solution
    - Drop existing problematic policies
    - Create simpler policies that don't cause recursion
    - Remove super admin policies that cause circular references
    - Keep basic user access policies that work correctly

  3. Changes
    - Drop "Super admins can manage all profiles" policy
    - Drop "Super admins can view all profiles" policy
    - Keep "Users can view own profile" policy
    - Keep "Users can update own profile" policy
*/

-- Drop the problematic policies that cause infinite recursion
DROP POLICY IF EXISTS "Super admins can manage all profiles" ON user_profiles;
DROP POLICY IF EXISTS "Super admins can view all profiles" ON user_profiles;

-- The remaining policies are safe and don't cause recursion:
-- - "Users can view own profile" - uses auth.uid() = user_id (no recursion)
-- - "Users can update own profile" - uses auth.uid() = user_id (no recursion)

-- Note: Super admin functionality can be implemented at the application level
-- or through a different approach that doesn't cause RLS recursion