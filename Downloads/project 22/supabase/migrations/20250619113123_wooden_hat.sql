/*
  # Add username system to business cards

  1. Changes
    - Add unique username field to business_cards table (if not exists)
    - Create function to generate unique usernames
    - Update existing cards with usernames based on email
    - Add index for username lookups

  2. Username Format
    - Based on email prefix or name
    - Fallback to random string if conflicts
    - Always unique and URL-friendly
*/

-- Add username column if it doesn't exist
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'business_cards' AND column_name = 'username'
  ) THEN
    ALTER TABLE business_cards ADD COLUMN username text UNIQUE;
  END IF;
END $$;

-- Function to generate unique username
CREATE OR REPLACE FUNCTION generate_unique_username(base_name text)
RETURNS text AS $$
DECLARE
  clean_name text;
  candidate_username text;
  counter integer := 0;
BEGIN
  -- Clean the base name: lowercase, remove special chars, replace spaces with hyphens
  clean_name := lower(regexp_replace(base_name, '[^a-zA-Z0-9\s]', '', 'g'));
  clean_name := regexp_replace(clean_name, '\s+', '-', 'g');
  clean_name := trim(clean_name, '-');
  
  -- If clean_name is empty or too short, use 'user'
  IF length(clean_name) < 2 THEN
    clean_name := 'user';
  END IF;
  
  -- Limit to 20 characters
  clean_name := left(clean_name, 20);
  
  candidate_username := clean_name;
  
  -- Check if username exists and increment counter if needed
  WHILE EXISTS (SELECT 1 FROM business_cards WHERE username = candidate_username) LOOP
    counter := counter + 1;
    candidate_username := clean_name || '-' || counter;
  END LOOP;
  
  RETURN candidate_username;
END;
$$ LANGUAGE plpgsql;

-- Update existing business cards without usernames
UPDATE business_cards 
SET username = generate_unique_username(
  COALESCE(
    NULLIF(split_part(
      (SELECT email FROM auth.users WHERE id = business_cards.user_id), 
      '@', 1
    ), ''),
    name,
    'user'
  )
)
WHERE username IS NULL;

-- Make username NOT NULL after updating existing records
ALTER TABLE business_cards ALTER COLUMN username SET NOT NULL;

-- Create index for username lookups
CREATE INDEX IF NOT EXISTS idx_business_cards_username ON business_cards(username);