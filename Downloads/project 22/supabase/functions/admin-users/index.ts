import { createClient } from 'npm:@supabase/supabase-js@2';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'GET, PUT, OPTIONS',
};

interface Database {
  public: {
    Tables: {
      user_profiles: {
        Row: {
          id: string;
          user_id: string;
          user_type: string;
          subscription_status: string;
          max_offers: number;
          has_analytics: boolean;
          has_ecommerce: boolean;
          can_change_background: boolean;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          user_id: string;
          user_type?: string;
          subscription_status?: string;
          max_offers?: number;
          has_analytics?: boolean;
          has_ecommerce?: boolean;
          can_change_background?: boolean;
        };
        Update: {
          user_type?: string;
          subscription_status?: string;
          max_offers?: number;
          has_analytics?: boolean;
          has_ecommerce?: boolean;
          can_change_background?: boolean;
          updated_at?: string;
        };
      };
    };
  };
}

Deno.serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    // Get the authorization header
    const authHeader = req.headers.get('Authorization');
    if (!authHeader) {
      return new Response(
        JSON.stringify({ error: 'Missing authorization header' }),
        { 
          status: 401, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      );
    }

    // Create Supabase client with service role key for admin operations
    const supabaseAdmin = createClient<Database>(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    );

    // Create regular client to verify user permissions
    const supabase = createClient<Database>(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        },
        global: {
          headers: {
            Authorization: authHeader,
          },
        },
      }
    );

    // Verify the user is authenticated and has admin permissions
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      return new Response(
        JSON.stringify({ error: 'Unauthorized' }),
        { 
          status: 401, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      );
    }

    // Check if user has admin permissions
    const { data: userProfile, error: profileError } = await supabase
      .from('user_profiles')
      .select('user_type')
      .eq('user_id', user.id)
      .single();

    if (profileError || !userProfile || userProfile.user_type !== 'super_admin') {
      return new Response(
        JSON.stringify({ error: 'Insufficient permissions' }),
        { 
          status: 403, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      );
    }

    if (req.method === 'GET') {
      // Get all users with their profiles using admin client
      const { data: users, error: usersError } = await supabaseAdmin.auth.admin.listUsers();
      
      if (usersError) {
        throw usersError;
      }

      // Get user profiles
      const { data: profiles, error: profilesError } = await supabaseAdmin
        .from('user_profiles')
        .select('*');

      if (profilesError) {
        throw profilesError;
      }

      // Combine user data with profiles
      const usersWithProfiles = users.users.map(user => {
        const profile = profiles?.find(p => p.user_id === user.id);
        return {
          id: user.id,
          email: user.email,
          created_at: user.created_at,
          profile: profile ? {
            id: profile.id,
            userId: profile.user_id,
            userType: profile.user_type,
            subscriptionStatus: profile.subscription_status,
            maxOffers: profile.max_offers,
            hasAnalytics: profile.has_analytics,
            hasEcommerce: profile.has_ecommerce,
            canChangeBackground: profile.can_change_background,
            createdAt: profile.created_at,
            updatedAt: profile.updated_at
          } : null
        };
      });

      return new Response(
        JSON.stringify({ users: usersWithProfiles }),
        { 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      );
    }

    if (req.method === 'PUT') {
      const { userId, userType } = await req.json();

      if (!userId || !userType) {
        return new Response(
          JSON.stringify({ error: 'Missing userId or userType' }),
          { 
            status: 400, 
            headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
          }
        );
      }

      // Update user profile using admin client
      const { error: updateError } = await supabaseAdmin
        .from('user_profiles')
        .update({
          user_type: userType,
          max_offers: userType === 'free' ? 3 : -1,
          has_analytics: userType !== 'free',
          has_ecommerce: userType === 'super_unlimited',
          can_change_background: userType !== 'free',
          updated_at: new Date().toISOString()
        })
        .eq('user_id', userId);

      if (updateError) {
        throw updateError;
      }

      return new Response(
        JSON.stringify({ success: true }),
        { 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      );
    }

    return new Response(
      JSON.stringify({ error: 'Method not allowed' }),
      { 
        status: 405, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    );

  } catch (error) {
    console.error('Error in admin-users function:', error);
    return new Response(
      JSON.stringify({ error: error.message || 'Internal server error' }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    );
  }
});