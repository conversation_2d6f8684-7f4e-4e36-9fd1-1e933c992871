@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom Animations */
@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slide-in-right {
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slide-in-left {
  from {
    opacity: 0;
    transform: translateX(-100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-fade-in {
  animation: fade-in 0.3s ease-out;
}

.animate-slide-in-right {
  animation: slide-in-right 0.4s ease-out;
}

.animate-slide-in-left {
  animation: slide-in-left 0.4s ease-out;
}

/* Safe Area Support for Modern Mobile Devices */
@supports (padding: max(0px)) {
  .safe-area-top {
    padding-top: max(1rem, env(safe-area-inset-top));
  }
  
  .safe-area-bottom {
    padding-bottom: max(1.25rem, env(safe-area-inset-bottom));
  }
  
  .safe-area-left {
    padding-left: max(1rem, env(safe-area-inset-left));
  }
  
  .safe-area-right {
    padding-right: max(1rem, env(safe-area-inset-right));
  }
}

/* Mobile-specific optimizations */
@media (max-width: 768px) {
  .mobile-cta-container {
    padding-bottom: max(1.25rem, env(safe-area-inset-bottom));
  }

  .mobile-content {
    padding-top: max(1rem, env(safe-area-inset-top));
  }

  /* Improve touch targets for mobile */
  button, a {
    min-height: 44px;
    min-width: 44px;
  }

  /* Better sidebar scrolling on mobile */
  .sidebar-mobile-scroll {
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none;
    -ms-overflow-style: none;
  }

  .sidebar-mobile-scroll::-webkit-scrollbar {
    display: none;
  }

  /* Prevent body scroll when sidebar is open */
  body.sidebar-open {
    overflow: hidden;
  }

  /* Improve mobile navigation touch targets */
  .mobile-nav-item {
    padding: 12px 16px;
    margin: 2px 0;
  }

  /* Better mobile spacing */
  .mobile-safe-padding {
    padding-left: max(1rem, env(safe-area-inset-left));
    padding-right: max(1rem, env(safe-area-inset-right));
  }

  /* Landing page mobile optimizations */
  .mobile-landing-page {
    height: 100vh;
    height: 100dvh; /* Dynamic viewport height for better mobile support */
  }

  /* Ensure content is scrollable on mobile */
  .mobile-landing-content {
    max-height: calc(100vh - 120px);
    max-height: calc(100dvh - 120px);
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
  }

  /* Better prose styling for mobile */
  .prose-mobile {
    font-size: 14px;
    line-height: 1.6;
  }

  .prose-mobile h1 {
    font-size: 1.5rem !important;
    line-height: 1.3 !important;
    margin-bottom: 0.75rem !important;
  }

  .prose-mobile h2 {
    font-size: 1.25rem !important;
    line-height: 1.4 !important;
    margin-bottom: 0.5rem !important;
  }

  .prose-mobile h3 {
    font-size: 1.125rem !important;
    line-height: 1.4 !important;
    margin-bottom: 0.5rem !important;
  }

  .prose-mobile p {
    font-size: 14px !important;
    line-height: 1.6 !important;
    margin-bottom: 0.75rem !important;
  }

  .prose-mobile ul, .prose-mobile ol {
    font-size: 14px !important;
    margin-bottom: 0.75rem !important;
  }

  .prose-mobile li {
    font-size: 14px !important;
    line-height: 1.6 !important;
    margin-bottom: 0.25rem !important;
  }

  /* Ensure mobile landing pages use full viewport */
  .mobile-landing-overlay {
    height: 100vh;
    height: 100dvh;
    overflow: hidden;
  }

  /* Better mobile scrolling */
  .mobile-scroll-content {
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none;
    -ms-overflow-style: none;
  }

  .mobile-scroll-content::-webkit-scrollbar {
    display: none;
  }

  /* Sticky CTA button on mobile */
  .mobile-sticky-cta {
    position: sticky;
    bottom: 0;
    z-index: 10;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
  }
}

/* Quill Editor Customization */
.ql-editor {
  min-height: 200px;
  font-family: inherit;
  line-height: 1.6;
  font-size: 16px; /* Increased from default 14px */
}

.ql-toolbar {
  border-top: 1px solid #e5e7eb;
  border-left: 1px solid #e5e7eb;
  border-right: 1px solid #e5e7eb;
  border-bottom: none;
  background: #f9fafb;
}

.ql-container {
  border-bottom: 1px solid #e5e7eb;
  border-left: 1px solid #e5e7eb;
  border-right: 1px solid #e5e7eb;
  border-top: none;
}

.ql-editor.ql-blank::before {
  color: #9ca3af;
  font-style: normal;
  font-size: 16px; /* Match the editor font size */
}

/* Prose styling for landing page content */
.prose {
  max-width: none;
}

.prose h1 {
  font-size: 1.875rem;
  font-weight: 700;
  margin-bottom: 1rem;
  line-height: 1.2;
}

.prose h2 {
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
  margin-top: 1.5rem;
  line-height: 1.3;
}

.prose h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  margin-top: 1.25rem;
  line-height: 1.4;
}

.prose p {
  margin-bottom: 1rem;
  line-height: 1.7;
  font-size: 16px; /* Ensure consistent font size */
}

.prose ul, .prose ol {
  margin-bottom: 1rem;
  padding-left: 1.5rem;
}

.prose li {
  margin-bottom: 0.5rem;
  line-height: 1.6;
  font-size: 16px; /* Consistent font size for list items */
}

.prose blockquote {
  border-left: 4px solid currentColor;
  padding-left: 1rem;
  margin: 1.5rem 0;
  font-style: italic;
  opacity: 0.8;
  font-size: 16px; /* Consistent font size */
}

.prose img {
  max-width: 100%;
  height: auto;
  border-radius: 0.5rem;
  margin: 1.5rem 0;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.prose a {
  text-decoration: underline;
  text-underline-offset: 2px;
}

.prose a:hover {
  opacity: 0.8;
}

.prose strong {
  font-weight: 600;
}

.prose em {
  font-style: italic;
}

.prose code {
  background-color: rgba(0, 0, 0, 0.1);
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-size: 0.875em;
}

.prose pre {
  background-color: rgba(0, 0, 0, 0.05);
  padding: 1rem;
  border-radius: 0.5rem;
  overflow-x: auto;
  margin: 1rem 0;
}

/* Ensure all text in the editor has consistent sizing */
.ql-editor * {
  font-size: inherit !important;
}

/* Override Quill's default font sizes for better consistency */
.ql-editor h1 {
  font-size: 1.875rem !important;
}

.ql-editor h2 {
  font-size: 1.5rem !important;
}

.ql-editor h3 {
  font-size: 1.25rem !important;
}

.ql-editor p, .ql-editor div, .ql-editor span {
  font-size: 16px !important;
}

.ql-editor ul li, .ql-editor ol li {
  font-size: 16px !important;
}

/* Social Icon Hover Effects */
.social-icon-hover:hover {
  background: var(--hover-bg) !important;
}

.social-icon-hover:hover .social-icon {
  color: var(--hover-text) !important;
  transform: scale(1.1);
}