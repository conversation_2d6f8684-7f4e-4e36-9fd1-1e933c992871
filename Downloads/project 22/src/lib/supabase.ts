import { createClient } from '@supabase/supabase-js';

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables');
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey);

export type Database = {
  public: {
    Tables: {
      business_cards: {
        Row: {
          id: string;
          user_id: string;
          name: string;
          title: string;
          company: string;
          bio: string;
          email: string;
          phone: string;
          website: string;
          profile_image: string;
          social_links: any;
          theme: string;
          username: string;
          created_at: string;
          updated_at: string;
          background_image: string | null;
          page_background: any | null;
          cover_image: string | null;
          offers_title: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          name?: string;
          title?: string;
          company?: string;
          bio?: string;
          email?: string;
          phone?: string;
          website?: string;
          profile_image?: string;
          social_links?: any;
          theme?: string;
          username: string;
          created_at?: string;
          updated_at?: string;
          background_image?: string | null;
          page_background?: any | null;
          cover_image?: string | null;
          offers_title?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          name?: string;
          title?: string;
          company?: string;
          bio?: string;
          email?: string;
          phone?: string;
          website?: string;
          profile_image?: string;
          social_links?: any;
          theme?: string;
          username?: string;
          created_at?: string;
          updated_at?: string;
          background_image?: string | null;
          page_background?: any | null;
          cover_image?: string | null;
          offers_title?: string;
        };
      };
      offers: {
        Row: {
          id: string;
          user_id: string;
          business_card_id: string;
          title: string;
          description: string;
          button_text: string;
          landing_page: any;
          is_active: boolean;
          created_at: string;
          updated_at: string;
          order_index: number;
        };
        Insert: {
          id?: string;
          user_id: string;
          business_card_id: string;
          title?: string;
          description?: string;
          button_text?: string;
          landing_page?: any;
          is_active?: boolean;
          created_at?: string;
          updated_at?: string;
          order_index?: number;
        };
        Update: {
          id?: string;
          user_id?: string;
          business_card_id?: string;
          title?: string;
          description?: string;
          button_text?: string;
          landing_page?: any;
          is_active?: boolean;
          created_at?: string;
          updated_at?: string;
          order_index?: number;
        };
      };
      user_profiles: {
        Row: {
          id: string;
          user_id: string;
          user_type: string;
          subscription_status: string;
          subscription_start_date: string | null;
          subscription_end_date: string | null;
          max_offers: number;
          has_analytics: boolean;
          has_ecommerce: boolean;
          can_change_background: boolean;
          created_at: string;
          updated_at: string;
          last_username_change: string | null;
        };
        Insert: {
          id?: string;
          user_id: string;
          user_type?: string;
          subscription_status?: string;
          subscription_start_date?: string | null;
          subscription_end_date?: string | null;
          max_offers?: number;
          has_analytics?: boolean;
          has_ecommerce?: boolean;
          can_change_background?: boolean;
          created_at?: string;
          updated_at?: string;
          last_username_change?: string | null;
        };
        Update: {
          id?: string;
          user_id?: string;
          user_type?: string;
          subscription_status?: string;
          subscription_start_date?: string | null;
          subscription_end_date?: string | null;
          max_offers?: number;
          has_analytics?: boolean;
          has_ecommerce?: boolean;
          can_change_background?: boolean;
          created_at?: string;
          updated_at?: string;
          last_username_change?: string | null;
        };
      };
      subscriptions: {
        Row: {
          id: string;
          user_id: string;
          plan_type: string;
          status: string;
          amount: number;
          currency: string;
          billing_cycle: string;
          stripe_subscription_id: string | null;
          stripe_customer_id: string | null;
          current_period_start: string | null;
          current_period_end: string | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          plan_type: string;
          status?: string;
          amount: number;
          currency?: string;
          billing_cycle: string;
          stripe_subscription_id?: string | null;
          stripe_customer_id?: string | null;
          current_period_start?: string | null;
          current_period_end?: string | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          plan_type?: string;
          status?: string;
          amount?: number;
          currency?: string;
          billing_cycle?: string;
          stripe_subscription_id?: string | null;
          stripe_customer_id?: string | null;
          current_period_start?: string | null;
          current_period_end?: string | null;
          created_at?: string;
          updated_at?: string;
        };
      };
      user_analytics: {
        Row: {
          id: string;
          user_id: string;
          business_card_id: string;
          event_type: string;
          event_data: any;
          ip_address: string | null;
          user_agent: string | null;
          referrer: string | null;
          created_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          business_card_id: string;
          event_type: string;
          event_data?: any;
          ip_address?: string | null;
          user_agent?: string | null;
          referrer?: string | null;
          created_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          business_card_id?: string;
          event_type?: string;
          event_data?: any;
          ip_address?: string | null;
          user_agent?: string | null;
          referrer?: string | null;
          created_at?: string;
        };
      };
      ecommerce_settings: {
        Row: {
          id: string;
          user_id: string;
          business_card_id: string;
          stripe_publishable_key: string | null;
          stripe_secret_key: string | null;
          paypal_client_id: string | null;
          payment_methods: any;
          tax_settings: any;
          shipping_settings: any;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          business_card_id: string;
          stripe_publishable_key?: string | null;
          stripe_secret_key?: string | null;
          paypal_client_id?: string | null;
          payment_methods?: any;
          tax_settings?: any;
          shipping_settings?: any;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          business_card_id?: string;
          stripe_publishable_key?: string | null;
          stripe_secret_key?: string | null;
          paypal_client_id?: string | null;
          payment_methods?: any;
          tax_settings?: any;
          shipping_settings?: any;
          created_at?: string;
          updated_at?: string;
        };
      };
      saved_cards: {
        Row: {
          id: string;
          user_id: string;
          saved_card_data: any;
          saved_at: string;
          notes: string | null;
          tags: string[] | null;
          updated_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          saved_card_data: any;
          saved_at?: string;
          notes?: string | null;
          tags?: string[] | null;
          updated_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          saved_card_data?: any;
          saved_at?: string;
          notes?: string | null;
          tags?: string[] | null;
          updated_at?: string;
        };
      };
    };
  };
};