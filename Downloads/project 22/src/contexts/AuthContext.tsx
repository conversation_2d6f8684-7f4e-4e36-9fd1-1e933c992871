import React, { createContext, useContext, useEffect, useState } from 'react';
import { User, Session } from '@supabase/supabase-js';
import { supabase } from '../lib/supabase';
import { UserProfile, UserType } from '../types';

interface AuthContextType {
  user: User | null;
  session: Session | null;
  userProfile: UserProfile | null;
  loading: boolean;
  signUp: (email: string, password: string) => Promise<{ error: any }>;
  signIn: (email: string, password: string) => Promise<{ error: any }>;
  signInWithGoogle: () => Promise<{ error: any }>;
  signOut: () => Promise<void>;
  refreshUserProfile: () => Promise<void>;
  hasPermission: (permission: 'analytics' | 'ecommerce' | 'unlimited_offers' | 'change_background' | 'admin') => boolean;
  isUserType: (type: UserType) => boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Get initial session
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session);
      setUser(session?.user ?? null);
      if (session?.user) {
        loadUserProfile(session.user.id);
      } else {
        setLoading(false);
      }
    });

    // Listen for auth changes
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange((_event, session) => {
      setSession(session);
      setUser(session?.user ?? null);
      if (session?.user) {
        loadUserProfile(session.user.id);
      } else {
        setUserProfile(null);
        setLoading(false);
      }
    });

    return () => subscription.unsubscribe();
  }, []);

  const loadUserProfile = async (userId: string) => {
    try {
      const { data, error } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('user_id', userId)
        .limit(1);

      if (data && data.length > 0 && !error) {
        const profileData = data[0];
        const profile: UserProfile = {
          id: profileData.id,
          userId: profileData.user_id,
          userType: profileData.user_type,
          subscriptionStatus: profileData.subscription_status,
          subscriptionStartDate: profileData.subscription_start_date,
          subscriptionEndDate: profileData.subscription_end_date,
          maxOffers: profileData.max_offers,
          hasAnalytics: profileData.has_analytics,
          hasEcommerce: profileData.has_ecommerce,
          canChangeBackground: profileData.can_change_background,
          createdAt: profileData.created_at,
          updatedAt: profileData.updated_at
        };
        setUserProfile(profile);
      } else {
        // No profile found, set to null
        setUserProfile(null);
      }
    } catch (error) {
      console.error('Error loading user profile:', error);
      setUserProfile(null);
    } finally {
      setLoading(false);
    }
  };

  const refreshUserProfile = async () => {
    if (user) {
      await loadUserProfile(user.id);
    }
  };

  const signUp = async (email: string, password: string) => {
    const { error } = await supabase.auth.signUp({
      email,
      password,
    });

    return { error };
  };

  const signIn = async (email: string, password: string) => {
    const { error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });
    return { error };
  };

  const signInWithGoogle = async () => {
    const { error } = await supabase.auth.signInWithOAuth({
      provider: 'google',
      options: {
        redirectTo: `${window.location.origin}/dashboard`,
        queryParams: {
          access_type: 'offline',
          prompt: 'consent',
        },
      },
    });
    return { error };
  };

  const signOut = async () => {
    try {
      // Clear local state immediately
      setUser(null);
      setSession(null);
      setUserProfile(null);
      setLoading(false);
      
      // Sign out from Supabase
      const { error } = await supabase.auth.signOut();
      
      if (error) {
        console.error('Error signing out:', error);
      }
      
      // Clear any cached data
      localStorage.clear();
      sessionStorage.clear();
      
    } catch (error) {
      console.error('Error during sign out:', error);
      // Even if there's an error, clear the local state
      setUser(null);
      setSession(null);
      setUserProfile(null);
      setLoading(false);
    }
  };

  const hasPermission = (permission: 'analytics' | 'ecommerce' | 'unlimited_offers' | 'change_background' | 'admin'): boolean => {
    if (!userProfile) return false;

    switch (permission) {
      case 'analytics':
        return userProfile.hasAnalytics;
      case 'ecommerce':
        return userProfile.hasEcommerce;
      case 'unlimited_offers':
        return userProfile.maxOffers === -1;
      case 'change_background':
        return userProfile.canChangeBackground;
      case 'admin':
        return userProfile.userType === 'super_admin';
      default:
        return false;
    }
  };

  const isUserType = (type: UserType): boolean => {
    return userProfile?.userType === type;
  };

  const value = {
    user,
    session,
    userProfile,
    loading,
    signUp,
    signIn,
    signInWithGoogle,
    signOut,
    refreshUserProfile,
    hasPermission,
    isUserType,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}