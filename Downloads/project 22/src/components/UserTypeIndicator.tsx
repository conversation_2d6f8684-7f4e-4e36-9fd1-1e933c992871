import React from 'react';
import { Crown, Zap, Star, Gift, User } from 'lucide-react';
import { UserType } from '../types';

interface UserTypeIndicatorProps {
  userType: UserType;
  className?: string;
}

export default function UserTypeIndicator({ userType, className = '' }: UserTypeIndicatorProps) {
  const getTypeConfig = (type: UserType) => {
    switch (type) {
      case 'super_admin':
        return {
          icon: Crown,
          label: 'Super Admin',
          color: 'text-purple-600',
          bgColor: 'bg-purple-100',
          borderColor: 'border-purple-200'
        };
      case 'super_unlimited':
        return {
          icon: Zap,
          label: 'Super Unlimited',
          color: 'text-yellow-600',
          bgColor: 'bg-yellow-100',
          borderColor: 'border-yellow-200'
        };
      case 'unlimited_yearly':
        return {
          icon: Star,
          label: 'Unlimited (Yearly)',
          color: 'text-blue-600',
          bgColor: 'bg-blue-100',
          borderColor: 'border-blue-200'
        };
      case 'unlimited_monthly':
        return {
          icon: Star,
          label: 'Unlimited (Monthly)',
          color: 'text-green-600',
          bgColor: 'bg-green-100',
          borderColor: 'border-green-200'
        };
      case 'free':
        return {
          icon: Gift,
          label: 'Free',
          color: 'text-gray-600',
          bgColor: 'bg-gray-100',
          borderColor: 'border-gray-200'
        };
      default:
        return {
          icon: User,
          label: 'Unknown',
          color: 'text-gray-600',
          bgColor: 'bg-gray-100',
          borderColor: 'border-gray-200'
        };
    }
  };

  const config = getTypeConfig(userType);
  const IconComponent = config.icon;

  return (
    <div className={`inline-flex items-center px-2 sm:px-3 py-1 rounded-full border ${config.bgColor} ${config.borderColor} ${className}`}>
      <IconComponent className={`w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2 ${config.color}`} />
      <span className={`text-xs sm:text-sm font-medium ${config.color}`}>
        {config.label}
      </span>
    </div>
  );
}