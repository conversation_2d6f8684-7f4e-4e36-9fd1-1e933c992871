import React, { useState, useEffect } from 'react';
import { Users, CreditCard, DollarSign, BarChart3, Settings, Search, Filter, Eye, Edit, Trash2 } from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';
import { supabase } from '../lib/supabase';
import UserTypeIndicator from './UserTypeIndicator';
import { UserProfile, UserType } from '../types';

interface AdminStats {
  totalUsers: number;
  activeSubscriptions: number;
  totalRevenue: number;
  totalCards: number;
}

interface UserWithProfile {
  id: string;
  email?: string;
  created_at?: string;
  profile: UserProfile | null;
}

export default function AdminDashboard() {
  const { hasPermission, session } = useAuth();
  const [stats, setStats] = useState<AdminStats>({
    totalUsers: 0,
    activeSubscriptions: 0,
    totalRevenue: 0,
    totalCards: 0
  });
  const [users, setUsers] = useState<UserWithProfile[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState<UserType | 'all'>('all');
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (hasPermission('admin')) {
      loadAdminData();
    }
  }, [hasPermission]);

  const loadAdminData = async () => {
    try {
      setError(null);
      // Load stats (these can still be loaded from client-side)
      const [usersResult, subscriptionsResult, cardsResult] = await Promise.all([
        supabase.from('user_profiles').select('*'),
        supabase.from('subscriptions').select('amount').eq('status', 'active'),
        supabase.from('business_cards').select('id')
      ]);

      const totalUsers = usersResult.data?.length || 0;
      const activeSubscriptions = subscriptionsResult.data?.length || 0;
      const totalRevenue = subscriptionsResult.data?.reduce((sum, sub) => sum + (sub.amount || 0), 0) || 0;
      const totalCards = cardsResult.data?.length || 0;

      setStats({
        totalUsers,
        activeSubscriptions,
        totalRevenue,
        totalCards
      });

      // Load users via edge function
      await loadUsersFromEdgeFunction();
    } catch (error) {
      console.error('Error loading admin data:', error);
      setError('Failed to load admin data');
    } finally {
      setLoading(false);
    }
  };

  const loadUsersFromEdgeFunction = async () => {
    try {
      if (!session?.access_token) {
        console.error('No access token available');
        throw new Error('No access token available');
      }

      const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
      if (!supabaseUrl) {
        throw new Error('VITE_SUPABASE_URL environment variable is not set');
      }

      const functionUrl = `${supabaseUrl}/functions/v1/admin-users`;
      console.log('Calling edge function at:', functionUrl);

      const response = await fetch(functionUrl, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Edge function error response:', errorText);
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }

      const data = await response.json();
      setUsers(data.users || []);
    } catch (error) {
      console.error('Error loading users from edge function:', error);
      setError(`Failed to load users: ${error.message}`);
      
      // Fallback to client-side data without emails
      try {
        const { data: profiles } = await supabase.from('user_profiles').select('*');
        const usersWithProfiles: UserWithProfile[] = profiles?.map(profile => ({
          id: profile.user_id,
          email: undefined,
          created_at: profile.created_at,
          profile: profile
        })) || [];
        setUsers(usersWithProfiles);
        setError('Loaded users without email data (edge function unavailable)');
      } catch (fallbackError) {
        console.error('Fallback also failed:', fallbackError);
        setError('Failed to load users even with fallback method');
      }
    }
  };

  const updateUserType = async (userId: string, newType: UserType) => {
    try {
      setError(null);
      if (!session?.access_token) {
        throw new Error('No access token available');
      }

      const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
      if (!supabaseUrl) {
        throw new Error('VITE_SUPABASE_URL environment variable is not set');
      }

      const response = await fetch(`${supabaseUrl}/functions/v1/admin-users`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId,
          userType: newType
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update user');
      }

      // Reload users after successful update
      await loadUsersFromEdgeFunction();
    } catch (error) {
      console.error('Error updating user type:', error);
      setError(`Failed to update user: ${error.message}`);
      
      // Fallback to client-side update
      try {
        const { error } = await supabase
          .from('user_profiles')
          .update({
            user_type: newType,
            max_offers: newType === 'free' ? 3 : -1,
            has_analytics: newType !== 'free',
            has_ecommerce: newType === 'super_unlimited',
            can_change_background: newType !== 'free',
            updated_at: new Date().toISOString()
          })
          .eq('user_id', userId);

        if (!error) {
          await loadAdminData();
          setError('User updated using fallback method');
        }
      } catch (fallbackError) {
        console.error('Fallback update also failed:', fallbackError);
        setError('Failed to update user even with fallback method');
      }
    }
  };

  const filteredUsers = users.filter(user => {
    const searchableText = (user.email || user.id || '').toLowerCase();
    const matchesSearch = searchableText.includes(searchTerm.toLowerCase());
    const matchesFilter = filterType === 'all' || user.profile?.userType === filterType;
    return matchesSearch && matchesFilter;
  });

  if (!hasPermission('admin')) {
    return (
      <div className="text-center py-12">
        <h2 className="text-2xl font-bold text-gray-900 mb-4">Access Denied</h2>
        <p className="text-gray-600">You don't have permission to access the admin dashboard.</p>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="text-center py-12">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p className="text-gray-600">Loading admin dashboard...</p>
      </div>
    );
  }

  return (
    <div className="space-y-6 sm:space-y-8">
      {/* Error Alert */}
      {error && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-yellow-800">{error}</p>
            </div>
            <div className="ml-auto pl-3">
              <div className="-mx-1.5 -my-1.5">
                <button
                  type="button"
                  onClick={() => setError(null)}
                  className="inline-flex bg-yellow-50 rounded-md p-1.5 text-yellow-500 hover:bg-yellow-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-yellow-50 focus:ring-yellow-600"
                >
                  <span className="sr-only">Dismiss</span>
                  <svg className="h-3 w-3" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Stats Cards */}
      <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
        <div className="bg-white rounded-xl shadow-lg p-4 sm:p-6">
          <div className="flex items-center justify-between mb-2 sm:mb-4">
            <h3 className="text-sm sm:text-lg font-semibold text-gray-900">Total Users</h3>
            <Users className="w-5 h-5 sm:w-6 sm:h-6 text-blue-600" />
          </div>
          <div className="text-2xl sm:text-3xl font-bold text-gray-900">{stats.totalUsers}</div>
        </div>

        <div className="bg-white rounded-xl shadow-lg p-4 sm:p-6">
          <div className="flex items-center justify-between mb-2 sm:mb-4">
            <h3 className="text-sm sm:text-lg font-semibold text-gray-900">Active Subscriptions</h3>
            <CreditCard className="w-5 h-5 sm:w-6 sm:h-6 text-green-600" />
          </div>
          <div className="text-2xl sm:text-3xl font-bold text-gray-900">{stats.activeSubscriptions}</div>
        </div>

        <div className="bg-white rounded-xl shadow-lg p-4 sm:p-6">
          <div className="flex items-center justify-between mb-2 sm:mb-4">
            <h3 className="text-sm sm:text-lg font-semibold text-gray-900">Monthly Revenue</h3>
            <DollarSign className="w-5 h-5 sm:w-6 sm:h-6 text-yellow-600" />
          </div>
          <div className="text-2xl sm:text-3xl font-bold text-gray-900">${stats.totalRevenue.toFixed(0)}</div>
        </div>

        <div className="bg-white rounded-xl shadow-lg p-4 sm:p-6">
          <div className="flex items-center justify-between mb-2 sm:mb-4">
            <h3 className="text-sm sm:text-lg font-semibold text-gray-900">Business Cards</h3>
            <BarChart3 className="w-5 h-5 sm:w-6 sm:h-6 text-purple-600" />
          </div>
          <div className="text-2xl sm:text-3xl font-bold text-gray-900">{stats.totalCards}</div>
        </div>
      </div>

      {/* Users Management */}
      <div className="bg-white rounded-xl shadow-lg">
        <div className="p-4 sm:p-6 border-b border-gray-200">
          <h2 className="text-lg sm:text-xl font-semibold text-gray-900 mb-4">User Management</h2>
          
          {/* Search and Filter */}
          <div className="flex flex-col space-y-4 sm:flex-row sm:space-y-0 sm:space-x-4">
            {/* Search */}
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 sm:w-5 sm:h-5 text-gray-400" />
              <input
                type="text"
                placeholder="Search users by email or ID..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-9 sm:pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm sm:text-base"
              />
            </div>

            {/* Tag Filter */}
            <div className="relative">
              <Filter className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 sm:w-5 sm:h-5 text-gray-400" />
              <select
                value={filterType}
                onChange={(e) => setFilterType(e.target.value as UserType | 'all')}
                className="pl-9 sm:pl-10 pr-8 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm sm:text-base"
              >
                <option value="all">All Users</option>
                <option value="free">Free</option>
                <option value="unlimited_monthly">Unlimited Monthly</option>
                <option value="unlimited_yearly">Unlimited Yearly</option>
                <option value="super_unlimited">Super Unlimited</option>
                <option value="super_admin">Super Admin</option>
              </select>
            </div>
          </div>
        </div>

        {/* Users Table - Mobile Responsive */}
        <div className="overflow-x-auto">
          {/* Desktop Table */}
          <div className="hidden sm:block">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    User
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Plan
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Joined
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredUsers.map((user) => (
                  <tr key={user.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          {user.email || 'N/A'}
                        </div>
                        <div className="text-sm text-gray-500 truncate max-w-xs">{user.id}</div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {user.profile ? (
                        <UserTypeIndicator userType={user.profile.userType} />
                      ) : (
                        <span className="text-sm text-gray-500">No profile</span>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {user.created_at ? new Date(user.created_at).toLocaleDateString() : 'N/A'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center space-x-2">
                        <select
                          value={user.profile?.userType || 'free'}
                          onChange={(e) => updateUserType(user.id, e.target.value as UserType)}
                          className="text-sm border border-gray-300 rounded px-2 py-1 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        >
                          <option value="free">Free</option>
                          <option value="unlimited_monthly">Unlimited Monthly</option>
                          <option value="unlimited_yearly">Unlimited Yearly</option>
                          <option value="super_unlimited">Super Unlimited</option>
                          <option value="super_admin">Super Admin</option>
                        </select>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Mobile Cards */}
          <div className="sm:hidden">
            <div className="space-y-4 p-4">
              {filteredUsers.map((user) => (
                <div key={user.id} className="bg-gray-50 rounded-lg p-4 space-y-3">
                  <div>
                    <div className="font-medium text-gray-900 text-sm">
                      {user.email || 'N/A'}
                    </div>
                    <div className="text-xs text-gray-500 break-all">{user.id}</div>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div>
                      {user.profile ? (
                        <UserTypeIndicator userType={user.profile.userType} className="text-xs" />
                      ) : (
                        <span className="text-xs text-gray-500">No profile</span>
                      )}
                    </div>
                    <div className="text-xs text-gray-500">
                      {user.created_at ? new Date(user.created_at).toLocaleDateString() : 'N/A'}
                    </div>
                  </div>

                  <div>
                    <label className="block text-xs font-medium text-gray-700 mb-1">
                      Change User Type:
                    </label>
                    <select
                      value={user.profile?.userType || 'free'}
                      onChange={(e) => updateUserType(user.id, e.target.value as UserType)}
                      className="w-full text-sm border border-gray-300 rounded px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="free">Free</option>
                      <option value="unlimited_monthly">Unlimited Monthly</option>
                      <option value="unlimited_yearly">Unlimited Yearly</option>
                      <option value="super_unlimited">Super Unlimited</option>
                      <option value="super_admin">Super Admin</option>
                    </select>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}