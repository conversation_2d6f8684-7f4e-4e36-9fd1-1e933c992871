import React, { useState, useEffect } from 'react';
import { BarChart3, Eye, Mail, Users, Zap, Edit3, ArrowRight, TrendingUp, Calendar, Globe } from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
import { BusinessCard, Offer } from '../../types';
import { getAnalytics } from '../../utils/analytics';
import BusinessCardComponent from '../BusinessCard';

interface DashboardHomeProps {
  businessCard: BusinessCard;
  offers: Offer[];
  onEditCard: () => void;
  onUpgradeClick?: () => void;
}

export default function DashboardHome({ businessCard, offers, onEditCard, onUpgradeClick }: DashboardHomeProps) {
  const { user, hasPermission } = useAuth();
  const [analytics, setAnalytics] = useState({
    totalViews: 0,
    contactClicks: 0,
    offerClicks: 0,
    socialClicks: 0,
    growth: { totalViews: 0, contactClicks: 0, offerClicks: 0, socialClicks: 0 }
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (hasPermission('analytics') && businessCard && user) {
      loadAnalytics();
    } else {
      setLoading(false);
    }
  }, [hasPermission, businessCard, user]);

  const loadAnalytics = async () => {
    if (!businessCard || !user) return;
    
    try {
      const analyticsData = await getAnalytics(user.id, businessCard.id);
      setAnalytics(analyticsData);
    } catch (error) {
      console.error('Error loading analytics:', error);
    } finally {
      setLoading(false);
    }
  };

  const activeOffers = offers.filter(offer => offer.isActive);

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-neutral-900 mb-2">Welcome back, {businessCard.name}! 👋</h1>
          <p className="text-neutral-600 text-lg">Here's how your business card is performing</p>
        </div>
        <div className="text-right">
          <p className="text-sm text-neutral-500">Last updated</p>
          <p className="text-sm font-medium text-neutral-700">{new Date().toLocaleDateString()}</p>
        </div>
      </div>

      {/* Quick Stats */}
      {hasPermission('analytics') ? (
        <div className="grid md:grid-cols-4 gap-6">
          {[
            { 
              title: 'Card Views', 
              value: analytics.totalViews, 
              growth: analytics.growth.totalViews, 
              color: 'from-primary-500 to-primary-600', 
              icon: Eye,
              description: 'Total profile visits'
            },
            { 
              title: 'Contact Clicks', 
              value: analytics.contactClicks, 
              growth: analytics.growth.contactClicks, 
              color: 'from-accent-500 to-accent-600', 
              icon: Mail,
              description: 'Email & phone taps'
            },
            { 
              title: 'Offer Clicks', 
              value: analytics.offerClicks, 
              growth: analytics.growth.offerClicks, 
              color: 'from-secondary-500 to-secondary-600', 
              icon: Zap,
              description: 'Special offer engagement'
            },
            { 
              title: 'Social Clicks', 
              value: analytics.socialClicks, 
              growth: analytics.growth.socialClicks, 
              color: 'from-warning-500 to-warning-600', 
              icon: Users,
              description: 'Social media visits'
            }
          ].map((stat, index) => (
            <div key={index} className="bg-white/70 backdrop-blur-sm rounded-2xl shadow-soft p-6 border border-white/50 hover:shadow-medium transition-all duration-200">
              <div className="flex items-center justify-between mb-4">
                <div className={`w-12 h-12 bg-gradient-to-br ${stat.color} rounded-2xl flex items-center justify-center`}>
                  <stat.icon className="w-6 h-6 text-white" />
                </div>
                <div className={`flex items-center text-sm font-medium ${stat.growth >= 0 ? 'text-accent-600' : 'text-red-500'}`}>
                  <TrendingUp className={`w-4 h-4 mr-1 ${stat.growth < 0 ? 'rotate-180' : ''}`} />
                  {stat.growth >= 0 ? '+' : ''}{stat.growth}%
                </div>
              </div>
              <div className="text-3xl font-bold text-neutral-900 mb-1">{stat.value.toLocaleString()}</div>
              <div className="text-sm text-neutral-600">{stat.description}</div>
            </div>
          ))}
        </div>
      ) : (
        <div className="bg-gradient-to-r from-primary-50 to-secondary-50 rounded-2xl p-8 border border-primary-200">
          <div className="text-center">
            <BarChart3 className="w-16 h-16 text-primary-500 mx-auto mb-4" />
            <h3 className="text-xl font-bold text-neutral-900 mb-2">Unlock Analytics</h3>
            <p className="text-neutral-600 mb-4">Get detailed insights about your card performance with our analytics dashboard</p>
            <button 
              onClick={onUpgradeClick}
              className="bg-gradient-to-r from-primary-500 to-primary-600 text-white px-6 py-3 rounded-2xl hover:from-primary-600 hover:to-primary-700 transition-all duration-200 font-medium"
            >
              Upgrade to Unlimited
            </button>
          </div>
        </div>
      )}

      <div className="grid lg:grid-cols-2 gap-8">
        {/* Card Preview */}
        <div className="bg-white/70 backdrop-blur-sm rounded-2xl shadow-soft p-8 border border-white/50">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-bold text-neutral-900">Your Business Card</h2>
            <button
              onClick={onEditCard}
              className="flex items-center text-primary-600 hover:text-primary-700 transition-colors font-medium"
            >
              <Edit3 className="w-4 h-4 mr-2" />
              Edit Card
            </button>
          </div>
          
          <div className="bg-gradient-to-br from-neutral-50 to-primary-50 rounded-2xl p-6">
            <BusinessCardComponent 
              card={businessCard} 
              offers={activeOffers}
              isCompact={true}
            />
          </div>
        </div>

        {/* Quick Actions & Info */}
        <div className="space-y-6">
          {/* Active Offers */}
          <div className="bg-white/70 backdrop-blur-sm rounded-2xl shadow-soft p-6 border border-white/50">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-bold text-neutral-900 flex items-center">
                <Zap className="w-5 h-5 mr-2 text-primary-500" />
                Active Offers
              </h3>
              <span className="text-2xl font-bold text-primary-600">{activeOffers.length}</span>
            </div>
            {activeOffers.length > 0 ? (
              <div className="space-y-3">
                {activeOffers.slice(0, 3).map((offer) => (
                  <div key={offer.id} className="bg-gradient-to-r from-primary-50 to-secondary-50 rounded-xl p-3">
                    <h4 className="font-medium text-neutral-900 text-sm">{offer.title}</h4>
                    <p className="text-xs text-neutral-600 mt-1">{offer.description.substring(0, 60)}...</p>
                  </div>
                ))}
                {activeOffers.length > 3 && (
                  <p className="text-sm text-neutral-500 text-center">+{activeOffers.length - 3} more offers</p>
                )}
              </div>
            ) : (
              <p className="text-neutral-600 text-sm">No active offers yet. Create your first offer to engage visitors!</p>
            )}
          </div>

          {/* Quick Stats */}
          <div className="bg-white/70 backdrop-blur-sm rounded-2xl shadow-soft p-6 border border-white/50">
            <h3 className="text-lg font-bold text-neutral-900 mb-4 flex items-center">
              <Globe className="w-5 h-5 mr-2 text-accent-500" />
              Quick Info
            </h3>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-neutral-600">Card Created</span>
                <span className="font-medium text-neutral-900">
                  {new Date().toLocaleDateString()}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-neutral-600">Total Offers</span>
                <span className="font-medium text-neutral-900">{offers.length}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-neutral-600">Social Links</span>
                <span className="font-medium text-neutral-900">{businessCard.socialLinks.length}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-neutral-600">Theme</span>
                <span className="font-medium text-neutral-900 capitalize">{businessCard.theme}</span>
              </div>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="bg-gradient-to-r from-secondary-50 to-accent-50 rounded-2xl p-6 border border-secondary-200">
            <h3 className="text-lg font-bold text-neutral-900 mb-4">Quick Actions</h3>
            <div className="space-y-3">
              <button 
                onClick={onEditCard}
                className="w-full flex items-center justify-between p-3 bg-white/70 rounded-xl hover:bg-white transition-colors"
              >
                <span className="font-medium text-neutral-900">Edit Business Card</span>
                <ArrowRight className="w-4 h-4 text-neutral-500" />
              </button>
              <button className="w-full flex items-center justify-between p-3 bg-white/70 rounded-xl hover:bg-white transition-colors">
                <span className="font-medium text-neutral-900">Create New Offer</span>
                <ArrowRight className="w-4 h-4 text-neutral-500" />
              </button>
              <button className="w-full flex items-center justify-between p-3 bg-white/70 rounded-xl hover:bg-white transition-colors">
                <span className="font-medium text-neutral-900">Share Your Card</span>
                <ArrowRight className="w-4 h-4 text-neutral-500" />
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}