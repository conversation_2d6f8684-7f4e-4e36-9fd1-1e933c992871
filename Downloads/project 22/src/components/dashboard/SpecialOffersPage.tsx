import React, { useState } from 'react';
import { Zap, Plus, Edit3, Trash2, Eye, Crown, ToggleLeft, ToggleRight, GripVertical, ArrowUp, ArrowDown } from 'lucide-react';
import { BusinessCard, Offer, UserProfile } from '../../types';
import { supabase } from '../../lib/supabase';
import { useAuth } from '../../contexts/AuthContext';
import OfferModal from '../OfferModal';

interface SpecialOffersPageProps {
  businessCard: BusinessCard;
  offers: Offer[];
  onOffersUpdate: (offers: Offer[]) => void;
  userProfile: UserProfile | null;
  hasPermission: (permission: string) => boolean;
  onUpgradeClick: () => void;
}

export default function SpecialOffersPage({ 
  businessCard, 
  offers, 
  onOffersUpdate, 
  userProfile, 
  hasPermission, 
  onUpgradeClick 
}: SpecialOffersPageProps) {
  const { user } = useAuth();
  const [showOfferModal, setShowOfferModal] = useState(false);
  const [editingOffer, setEditingOffer] = useState<Offer | null>(null);
  const [offerModalMode, setOfferModalMode] = useState<'create' | 'edit'>('create');
  const [draggedIndex, setDraggedIndex] = useState<number | null>(null);
  const [dragOverIndex, setDragOverIndex] = useState<number | null>(null);

  const handleCreateOffer = () => {
    if (!hasPermission('unlimited_offers')) {
      const activeOffers = offers.filter(o => o.isActive);
      if (userProfile && userProfile.maxOffers !== -1 && activeOffers.length >= userProfile.maxOffers) {
        onUpgradeClick();
        return;
      }
    }

    setEditingOffer(null);
    setOfferModalMode('create');
    setShowOfferModal(true);
  };

  const handleEditOffer = (offer: Offer) => {
    setEditingOffer(offer);
    setOfferModalMode('edit');
    setShowOfferModal(true);
  };

  const handleSaveOffer = async (offerData: Partial<Offer>) => {
    if (!businessCard || !user) return;

    try {
      if (offerModalMode === 'create') {
        const { data, error } = await supabase
          .from('offers')
          .insert({
            user_id: user.id,
            business_card_id: businessCard.id,
            title: offerData.title,
            description: offerData.description,
            button_text: offerData.buttonText,
            landing_page: offerData.landingPage,
            is_active: false,
            order_index: offers.length
          })
          .select()
          .single();

        if (data && !error) {
          const newOffer: Offer = {
            id: data.id,
            title: data.title,
            description: data.description,
            buttonText: data.button_text,
            landingPage: data.landing_page,
            isActive: data.is_active
          };
          onOffersUpdate([...offers, newOffer]);
        }
      } else if (editingOffer) {
        const { error } = await supabase
          .from('offers')
          .update({
            title: offerData.title,
            description: offerData.description,
            button_text: offerData.buttonText,
            landing_page: offerData.landingPage,
            updated_at: new Date().toISOString()
          })
          .eq('id', editingOffer.id);

        if (!error) {
          onOffersUpdate(offers.map(offer => 
            offer.id === editingOffer.id 
              ? { ...offer, ...offerData }
              : offer
          ));
        }
      }
    } catch (error) {
      console.error('Error saving offer:', error);
    }
  };

  const handleDeleteOffer = async (offerId: string) => {
    if (!confirm('Are you sure you want to delete this offer?')) return;

    try {
      const { error } = await supabase
        .from('offers')
        .delete()
        .eq('id', offerId);

      if (!error) {
        onOffersUpdate(offers.filter(offer => offer.id !== offerId));
      }
    } catch (error) {
      console.error('Error deleting offer:', error);
    }
  };

  const toggleOfferActive = async (offerId: string) => {
    try {
      const offer = offers.find(o => o.id === offerId);
      if (!offer) return;

      // Check if user has reached their offer limit
      const activeOffers = offers.filter(o => o.isActive);
      if (!offer.isActive && userProfile && userProfile.maxOffers !== -1 && activeOffers.length >= userProfile.maxOffers) {
        onUpgradeClick();
        return;
      }

      const { error } = await supabase
        .from('offers')
        .update({ is_active: !offer.isActive })
        .eq('id', offerId);

      if (!error) {
        onOffersUpdate(offers.map(o => 
          o.id === offerId ? { ...o, isActive: !o.isActive } : o
        ));
      }
    } catch (error) {
      console.error('Error toggling offer:', error);
    }
  };

  // Reordering functions
  const moveOffer = async (fromIndex: number, toIndex: number) => {
    if (fromIndex === toIndex) return;

    const newOffers = [...offers];
    const [movedOffer] = newOffers.splice(fromIndex, 1);
    newOffers.splice(toIndex, 0, movedOffer);

    // Update local state immediately for better UX
    onOffersUpdate(newOffers);

    // Update order_index in database
    try {
      const updates = newOffers.map((offer, index) => ({
        id: offer.id,
        order_index: index
      }));

      // Update all offers with new order
      for (const update of updates) {
        await supabase
          .from('offers')
          .update({ order_index: update.order_index })
          .eq('id', update.id);
      }
    } catch (error) {
      console.error('Error updating offer order:', error);
      // Revert on error
      onOffersUpdate(offers);
    }
  };

  const moveOfferUp = (index: number) => {
    if (index > 0) {
      moveOffer(index, index - 1);
    }
  };

  const moveOfferDown = (index: number) => {
    if (index < offers.length - 1) {
      moveOffer(index, index + 1);
    }
  };

  // Drag and drop handlers
  const handleDragStart = (e: React.DragEvent, index: number) => {
    setDraggedIndex(index);
    e.dataTransfer.effectAllowed = 'move';
    e.dataTransfer.setData('text/html', '');
  };

  const handleDragOver = (e: React.DragEvent, index: number) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
    setDragOverIndex(index);
  };

  const handleDragLeave = () => {
    setDragOverIndex(null);
  };

  const handleDrop = (e: React.DragEvent, dropIndex: number) => {
    e.preventDefault();
    if (draggedIndex !== null && draggedIndex !== dropIndex) {
      moveOffer(draggedIndex, dropIndex);
    }
    setDraggedIndex(null);
    setDragOverIndex(null);
  };

  const handleDragEnd = () => {
    setDraggedIndex(null);
    setDragOverIndex(null);
  };

  const activeOffers = offers.filter(offer => offer.isActive);

  return (
    <div className="space-y-6 sm:space-y-8">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div className="flex items-center">
          <Zap className="w-6 h-6 text-primary-500 mr-3" />
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold text-neutral-900">Special Offers</h1>
            <p className="text-neutral-600 mt-1">Create and manage your special offers</p>
            {userProfile && userProfile.maxOffers !== -1 && (
              <p className="text-sm text-neutral-500 mt-1">
                Active offers: {activeOffers.length}/{userProfile.maxOffers}
              </p>
            )}
          </div>
        </div>
        <button 
          onClick={handleCreateOffer}
          className="flex items-center bg-gradient-to-r from-primary-500 to-primary-600 text-white px-4 sm:px-6 py-3 rounded-2xl hover:from-primary-600 hover:to-primary-700 transition-all duration-200 font-medium shadow-colored transform hover:scale-105 w-full sm:w-auto justify-center"
        >
          <Plus className="w-4 h-4 mr-2" />
          Add Offer
        </button>
      </div>

      {/* Reordering Instructions */}
      {offers.length > 1 && (
        <div className="bg-gradient-to-r from-accent-50 to-primary-50 rounded-2xl p-4 border border-accent-200">
          <div className="flex items-start">
            <GripVertical className="w-5 h-5 text-accent-600 mr-3 mt-0.5 flex-shrink-0" />
            <p className="text-accent-800 font-medium text-sm">
              💡 <strong>Tip:</strong> Drag and drop offers to reorder them, or use the arrow buttons. The order here determines how they appear on your business card.
            </p>
          </div>
        </div>
      )}

      {/* Offers List */}
      <div className="space-y-4">
        {offers.length > 0 ? (
          offers.map((offer, index) => (
            <div 
              key={offer.id} 
              draggable
              onDragStart={(e) => handleDragStart(e, index)}
              onDragOver={(e) => handleDragOver(e, index)}
              onDragLeave={handleDragLeave}
              onDrop={(e) => handleDrop(e, index)}
              onDragEnd={handleDragEnd}
              className={`bg-white/70 backdrop-blur-sm rounded-2xl shadow-soft p-4 sm:p-6 border border-white/50 hover:shadow-medium transition-all duration-200 cursor-move ${
                draggedIndex === index ? 'opacity-50 scale-95' : ''
              } ${
                dragOverIndex === index && draggedIndex !== index ? 'border-primary-400 bg-primary-50/50' : ''
              }`}
            >
              <div className="flex flex-col sm:flex-row sm:items-start space-y-4 sm:space-y-0 sm:space-x-4">
                {/* Drag Handle & Order Controls */}
                <div className="flex items-center justify-between sm:flex-col sm:items-center sm:space-y-2 sm:pt-2">
                  <div className="flex items-center sm:flex-col sm:space-y-2">
                    <div className="flex items-center justify-center w-8 h-8 bg-neutral-100 rounded-lg text-neutral-500 font-bold text-sm">
                      {index + 1}
                    </div>
                    <GripVertical className="w-5 h-5 text-neutral-400 cursor-grab active:cursor-grabbing ml-2 sm:ml-0" />
                  </div>
                  <div className="flex space-x-1 sm:flex-col sm:space-x-0 sm:space-y-1">
                    <button
                      onClick={() => moveOfferUp(index)}
                      disabled={index === 0}
                      className="w-6 h-6 bg-neutral-100 rounded flex items-center justify-center hover:bg-neutral-200 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                      title="Move up"
                    >
                      <ArrowUp className="w-3 h-3 text-neutral-600" />
                    </button>
                    <button
                      onClick={() => moveOfferDown(index)}
                      disabled={index === offers.length - 1}
                      className="w-6 h-6 bg-neutral-100 rounded flex items-center justify-center hover:bg-neutral-200 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                      title="Move down"
                    >
                      <ArrowDown className="w-3 h-3 text-neutral-600" />
                    </button>
                  </div>
                </div>

                {/* Offer Content */}
                <div className="flex-1">
                  <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start mb-4 space-y-4 sm:space-y-0">
                    <div className="flex-1">
                      <div className="flex flex-col sm:flex-row sm:items-center mb-2 space-y-2 sm:space-y-0">
                        <h3 className="text-lg font-bold text-neutral-900 sm:mr-3">{offer.title}</h3>
                        <span className={`px-3 py-1 rounded-full text-sm font-medium w-fit ${
                          offer.isActive 
                            ? 'bg-gradient-to-r from-accent-100 to-accent-200 text-accent-800 border border-accent-300' 
                            : 'bg-neutral-100 text-neutral-600 border border-neutral-200'
                        }`}>
                          {offer.isActive ? 'Active' : 'Inactive'}
                        </span>
                      </div>
                      <p className="text-neutral-600 mb-4 leading-relaxed">{offer.description}</p>
                      <div className="flex flex-wrap items-center gap-4">
                        <button 
                          onClick={() => handleEditOffer(offer)}
                          className="flex items-center text-primary-600 hover:text-primary-700 transition-colors text-sm font-medium"
                        >
                          <Edit3 className="w-4 h-4 mr-1" />
                          Edit
                        </button>
                        <button 
                          onClick={() => handleDeleteOffer(offer.id)}
                          className="flex items-center text-red-500 hover:text-red-600 transition-colors text-sm font-medium"
                        >
                          <Trash2 className="w-4 h-4 mr-1" />
                          Delete
                        </button>
                      </div>
                    </div>
                    
                    <div className="flex items-center">
                      <button
                        onClick={() => toggleOfferActive(offer.id)}
                        className={`flex items-center px-4 py-2 rounded-xl font-medium transition-all duration-200 ${
                          offer.isActive
                            ? 'bg-accent-100 text-accent-700 hover:bg-accent-200'
                            : 'bg-neutral-100 text-neutral-600 hover:bg-neutral-200'
                        }`}
                      >
                        {offer.isActive ? (
                          <ToggleRight className="w-5 h-5 mr-2" />
                        ) : (
                          <ToggleLeft className="w-5 h-5 mr-2" />
                        )}
                        <span className="hidden sm:inline">
                          {offer.isActive ? 'Deactivate' : 'Activate'}
                        </span>
                        <span className="sm:hidden">
                          {offer.isActive ? 'Off' : 'On'}
                        </span>
                      </button>
                    </div>
                  </div>
                  
                  {/* Landing Page Preview */}
                  <div className="bg-gradient-to-r from-neutral-50 to-primary-50 rounded-2xl p-4 border border-neutral-200">
                    <div className="flex items-center mb-3">
                      <Eye className="w-4 h-4 text-primary-500 mr-2" />
                      <h4 className="font-bold text-neutral-900 text-sm">Landing Page Preview</h4>
                    </div>
                    <div className="grid gap-3 text-xs">
                      <div className="grid sm:grid-cols-2 gap-3">
                        <div>
                          <p className="text-neutral-600"><strong>Title:</strong> {offer.landingPage.title}</p>
                          <p className="text-neutral-600"><strong>Subtitle:</strong> {offer.landingPage.subtitle || 'None'}</p>
                        </div>
                        <div>
                          <p className="text-neutral-600"><strong>CTA Button:</strong> {offer.landingPage.ctaText}</p>
                          <p className="text-neutral-600 break-all"><strong>URL:</strong> 
                            <a href={offer.landingPage.ctaUrl} target="_blank" rel="noopener noreferrer" className="text-primary-600 hover:text-primary-700 ml-1">
                              {offer.landingPage.ctaUrl}
                            </a>
                          </p>
                        </div>
                      </div>
                      {offer.landingPage.content && (
                        <div className="pt-3 border-t border-neutral-200">
                          <p className="text-neutral-600">
                            <strong>Content Preview:</strong> 
                            <span className="ml-1">
                              {offer.landingPage.content.replace(/<[^>]*>/g, '').substring(0, 100)}...
                            </span>
                          </p>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))
        ) : (
          /* Empty State */
          <div className="text-center py-12 sm:py-16 bg-white/70 backdrop-blur-sm rounded-2xl shadow-soft border border-white/50">
            <div className="w-16 h-16 sm:w-20 sm:h-20 bg-gradient-to-br from-primary-100 to-secondary-100 rounded-3xl flex items-center justify-center mx-auto mb-6">
              <Zap className="w-8 h-8 sm:w-10 sm:h-10 text-primary-500" />
            </div>
            <h3 className="text-xl sm:text-2xl font-bold text-neutral-900 mb-3">No offers yet</h3>
            <p className="text-neutral-600 mb-6 sm:mb-8 max-w-md mx-auto leading-relaxed px-4">
              Create your first special offer to engage with your audience and drive more conversions from your business card.
            </p>
            <button 
              onClick={handleCreateOffer}
              className="bg-gradient-to-r from-primary-500 to-primary-600 text-white px-6 sm:px-8 py-3 sm:py-4 rounded-2xl hover:from-primary-600 hover:to-primary-700 transition-all duration-200 font-medium shadow-colored transform hover:scale-105"
            >
              Create Your First Offer
            </button>
          </div>
        )}
      </div>

      {/* Upgrade Prompt for Free Users */}
      {userProfile && userProfile.maxOffers !== -1 && (
        <div className="bg-gradient-to-r from-primary-50 to-secondary-50 rounded-2xl p-6 sm:p-8 border border-primary-200">
          <div className="flex flex-col sm:flex-row sm:items-center justify-between space-y-4 sm:space-y-0">
            <div className="flex items-start sm:items-center">
              <Crown className="w-6 h-6 sm:w-8 sm:h-8 text-primary-500 mr-4 flex-shrink-0 mt-1 sm:mt-0" />
              <div>
                <h3 className="text-lg sm:text-xl font-bold text-neutral-900 mb-1">Unlock Unlimited Offers</h3>
                <p className="text-neutral-600">
                  Upgrade to create unlimited special offers and landing pages for your business card.
                </p>
              </div>
            </div>
            <button 
              onClick={onUpgradeClick}
              className="bg-gradient-to-r from-primary-500 to-primary-600 text-white px-4 sm:px-6 py-3 rounded-2xl hover:from-primary-600 hover:to-primary-700 transition-all duration-200 font-medium shadow-colored transform hover:scale-105 w-full sm:w-auto"
            >
              Upgrade Now
            </button>
          </div>
        </div>
      )}

      {/* Offer Modal */}
      <OfferModal
        isOpen={showOfferModal}
        onClose={() => setShowOfferModal(false)}
        onSave={handleSaveOffer}
        offer={editingOffer}
        mode={offerModalMode}
      />
    </div>
  );
}