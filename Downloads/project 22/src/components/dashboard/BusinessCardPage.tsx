import React, { useState } from 'react';
import { Edit3, Eye, Save, X, Palette, User, Image as ImageIcon } from 'lucide-react';
import { BusinessCard, PageBackground } from '../../types';
import { supabase } from '../../lib/supabase';
import ImageUpload from '../ImageUpload';
import CoverImageUpload from '../CoverImageUpload';
import ThemeSelector from '../ThemeSelector';
import PageBackgroundSelector from '../PageBackgroundSelector';
import SocialLinksEditor from '../SocialLinksEditor';
import { getThemeGradient as getCardThemeGradient } from '../BusinessCard';

interface BusinessCardPageProps {
  businessCard: BusinessCard;
  onBusinessCardUpdate: (card: BusinessCard) => void;
  hasPermission: (permission: string) => boolean;
  onUpgradeClick: () => void;
}

export default function BusinessCardPage({ 
  businessCard, 
  onBusinessCardUpdate, 
  hasPermission, 
  onUpgradeClick 
}: BusinessCardPageProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [editingCard, setEditingCard] = useState<BusinessCard>(businessCard);
  const [activeSection, setActiveSection] = useState<'general' | 'design'>('general');
  const [saving, setSaving] = useState(false);

  const handleSaveCard = async () => {
    setSaving(true);
    try {
      const { error } = await supabase
        .from('business_cards')
        .update({
          name: editingCard.name,
          title: editingCard.title,
          company: editingCard.company,
          bio: editingCard.bio,
          email: editingCard.email,
          phone: editingCard.phone,
          website: editingCard.website,
          profile_image: editingCard.profileImage,
          background_image: editingCard.backgroundImage,
          cover_image: editingCard.coverImage,
          social_links: editingCard.socialLinks,
          theme: editingCard.theme,
          page_background: editingCard.pageBackground,
          offers_title: editingCard.offersTitle || 'Special Offers', // Save offers title
          location: editingCard.location || null, // Save location
          join_year: editingCard.joinYear || null, // Save join year
          updated_at: new Date().toISOString()
        })
        .eq('id', businessCard.id);

      if (!error) {
        onBusinessCardUpdate(editingCard);
        setIsEditing(false);
      }
    } catch (error) {
      console.error('Error saving card:', error);
    } finally {
      setSaving(false);
    }
  };

  const handleCancelEdit = () => {
    setEditingCard(businessCard);
    setIsEditing(false);
  };

  const handleThemeChange = (themeId: string) => {
    setEditingCard({ ...editingCard, theme: themeId as BusinessCard['theme'] });
  };

  const handleBackgroundImageChange = (imageUrl: string | null) => {
    setEditingCard({ ...editingCard, backgroundImage: imageUrl || undefined });
  };

  const handleCoverImageChange = (imageUrl: string | null) => {
    setEditingCard({ ...editingCard, coverImage: imageUrl || undefined });
  };

  const handlePageBackgroundChange = (background: PageBackground | null) => {
    setEditingCard({ ...editingCard, pageBackground: background || undefined });
  };

  const handleSocialLinksChange = (socialLinks: any[]) => {
    setEditingCard({ ...editingCard, socialLinks });
  };

  // Helper functions for page background styling
  const getPageBackgroundStyle = (pageBackground?: PageBackground) => {
    if (!pageBackground) {
      return 'bg-gradient-to-br from-neutral-50 to-primary-50';
    }

    switch (pageBackground.type) {
      case 'gradient':
        return pageBackground.value;
      case 'image':
        return '';
      case 'pattern':
        return pageBackground.value;
      default:
        return 'bg-gradient-to-br from-neutral-50 to-primary-50';
    }
  };

  const getPageBackgroundImage = (pageBackground?: PageBackground) => {
    if (pageBackground?.type === 'image') {
      return {
        backgroundImage: `url(${pageBackground.value})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat'
      };
    }
    if (pageBackground?.type === 'pattern') {
      return {
        backgroundImage: pageBackground.value,
        backgroundColor: '#f9fafb',
        backgroundSize: '20px 20px', // default, can be improved by storing patternSize
        backgroundRepeat: 'repeat'
      };
    }
    return {};
  };

  const getPageOverlay = (pageBackground?: PageBackground) => {
    if (pageBackground?.type === 'image' && pageBackground.overlay?.enabled) {
      return {
        backgroundColor: pageBackground.overlay.color,
        opacity: pageBackground.overlay.opacity
      };
    }
    return undefined;
  };

  return (
    <div className="space-y-6 sm:space-y-8">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div className="flex items-center">
          <User className="w-6 h-6 text-primary-500 mr-3" />
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold text-neutral-900">My Business Card</h1>
            <p className="text-neutral-600 mt-1">Customize your digital business card</p>
          </div>
        </div>
        {!isEditing ? (
          <button
            onClick={() => setIsEditing(true)}
            className="flex items-center bg-gradient-to-r from-primary-500 to-primary-600 text-white px-4 sm:px-6 py-3 rounded-2xl hover:from-primary-600 hover:to-primary-700 transition-all duration-200 font-medium shadow-colored transform hover:scale-105 w-full sm:w-auto justify-center"
          >
            <Edit3 className="w-4 h-4 mr-2" />
            Edit Card
          </button>
        ) : (
          <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3">
            <button
              onClick={handleSaveCard}
              disabled={saving}
              className="flex items-center bg-gradient-to-r from-accent-500 to-accent-600 text-white px-4 sm:px-6 py-3 rounded-2xl hover:from-accent-600 hover:to-accent-700 transition-all duration-200 font-medium shadow-colored transform hover:scale-105 disabled:opacity-50 justify-center"
            >
              <Save className="w-4 h-4 mr-2" />
              {saving ? 'Saving...' : 'Save Changes'}
            </button>
            <button
              onClick={handleCancelEdit}
              className="flex items-center text-neutral-600 hover:text-neutral-700 transition-colors font-medium px-4 py-3 justify-center"
            >
              <X className="w-4 h-4 mr-2" />
              Cancel
            </button>
          </div>
        )}
      </div>

      {/* Section Tabs */}
      <div className="flex space-x-1 bg-white/70 backdrop-blur-sm rounded-2xl p-1 border border-white/50">
        <button
          onClick={() => setActiveSection('general')}
          className={`flex-1 flex items-center justify-center px-4 sm:px-6 py-3 rounded-xl font-medium transition-all duration-200 ${
            activeSection === 'general'
              ? 'bg-gradient-to-r from-primary-500 to-primary-600 text-white shadow-colored'
              : 'text-neutral-600 hover:text-neutral-900 hover:bg-white/50'
          }`}
        >
          <User className="w-4 h-4 mr-2" />
          <span className="hidden sm:inline">General Info</span>
          <span className="sm:hidden">Info</span>
        </button>
        <button
          onClick={() => setActiveSection('design')}
          className={`flex-1 flex items-center justify-center px-4 sm:px-6 py-3 rounded-xl font-medium transition-all duration-200 ${
            activeSection === 'design'
              ? 'bg-gradient-to-r from-primary-500 to-primary-600 text-white shadow-colored'
              : 'text-neutral-600 hover:text-neutral-900 hover:bg-white/50'
          }`}
        >
          <Palette className="w-4 h-4 mr-2" />
          <span className="hidden sm:inline">Card Design</span>
          <span className="sm:hidden">Design</span>
        </button>
      </div>

      <div className="grid lg:grid-cols-2 gap-6 lg:gap-8">
        {/* Edit Form */}
        <div className="bg-white/70 backdrop-blur-sm rounded-2xl shadow-soft p-6 sm:p-8 border border-white/50">
          <h2 className="text-lg sm:text-xl font-bold text-neutral-900 mb-6 flex items-center">
            {activeSection === 'general' ? (
              <>
                <User className="w-5 h-5 mr-2 text-primary-500" />
                General Information
              </>
            ) : (
              <>
                <Palette className="w-5 h-5 mr-2 text-primary-500" />
                Design & Themes
              </>
            )}
          </h2>

          <div className="space-y-6 sm:space-y-8">
            {activeSection === 'general' ? (
              <>
                <div>
                  <label className="block text-sm font-semibold text-neutral-700 mb-3">
                    Profile Image
                  </label>
                  <ImageUpload
                    currentImage={editingCard.profileImage}
                    onImageChange={(imageUrl) => setEditingCard({...editingCard, profileImage: imageUrl})}
                    className={!isEditing ? 'opacity-50 pointer-events-none' : ''}
                  />
                </div>

                <div className="grid gap-6">
                  <div className="grid sm:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-semibold text-neutral-700 mb-2">
                        Full Name
                      </label>
                      <input
                        type="text"
                        value={editingCard.name}
                        onChange={(e) => setEditingCard({...editingCard, name: e.target.value})}
                        disabled={!isEditing}
                        className="w-full px-4 py-3 border border-neutral-200 rounded-2xl focus:ring-2 focus:ring-primary-500 focus:border-transparent disabled:bg-neutral-50 transition-all duration-200"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-semibold text-neutral-700 mb-2">
                        Job Title
                      </label>
                      <input
                        type="text"
                        value={editingCard.title}
                        onChange={(e) => setEditingCard({...editingCard, title: e.target.value})}
                        disabled={!isEditing}
                        className="w-full px-4 py-3 border border-neutral-200 rounded-2xl focus:ring-2 focus:ring-primary-500 focus:border-transparent disabled:bg-neutral-50 transition-all duration-200"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-semibold text-neutral-700 mb-2">
                      Company
                    </label>
                    <input
                      type="text"
                      value={editingCard.company}
                      onChange={(e) => setEditingCard({...editingCard, company: e.target.value})}
                      disabled={!isEditing}
                      className="w-full px-4 py-3 border border-neutral-200 rounded-2xl focus:ring-2 focus:ring-primary-500 focus:border-transparent disabled:bg-neutral-50 transition-all duration-200"
                    />
                  </div>

                  {/* Location Field */}
                  <div>
                    <label className="block text-sm font-semibold text-neutral-700 mb-2">
                      Location
                    </label>
                    <input
                      type="text"
                      value={editingCard.location || ''}
                      onChange={(e) => setEditingCard({...editingCard, location: e.target.value})}
                      disabled={!isEditing}
                      placeholder="Remote, New York, etc."
                      className="w-full px-4 py-3 border border-neutral-200 rounded-2xl focus:ring-2 focus:ring-primary-500 focus:border-transparent disabled:bg-neutral-50 transition-all duration-200"
                    />
                  </div>

                  {/* Join Year Field */}
                  <div>
                    <label className="block text-sm font-semibold text-neutral-700 mb-2">
                      Join Year
                    </label>
                    <input
                      type="number"
                      min="1900"
                      max={new Date().getFullYear()}
                      value={editingCard.joinYear || ''}
                      onChange={(e) => setEditingCard({...editingCard, joinYear: e.target.value ? parseInt(e.target.value) : undefined})}
                      disabled={!isEditing}
                      placeholder="2020"
                      className="w-full px-4 py-3 border border-neutral-200 rounded-2xl focus:ring-2 focus:ring-primary-500 focus:border-transparent disabled:bg-neutral-50 transition-all duration-200"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-semibold text-neutral-700 mb-2">
                      Bio
                    </label>
                    <textarea
                      value={editingCard.bio}
                      onChange={(e) => setEditingCard({...editingCard, bio: e.target.value})}
                      disabled={!isEditing}
                      rows={3}
                      className="w-full px-4 py-3 border border-neutral-200 rounded-2xl focus:ring-2 focus:ring-primary-500 focus:border-transparent disabled:bg-neutral-50 transition-all duration-200 resize-none"
                    />
                  </div>

                  <div className="grid sm:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-semibold text-neutral-700 mb-2">
                        Email
                      </label>
                      <input
                        type="email"
                        value={editingCard.email}
                        onChange={(e) => setEditingCard({...editingCard, email: e.target.value})}
                        disabled={!isEditing}
                        className="w-full px-4 py-3 border border-neutral-200 rounded-2xl focus:ring-2 focus:ring-primary-500 focus:border-transparent disabled:bg-neutral-50 transition-all duration-200"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-semibold text-neutral-700 mb-2">
                        Phone (for WhatsApp)
                      </label>
                      <input
                        type="tel"
                        value={editingCard.phone}
                        onChange={(e) => setEditingCard({...editingCard, phone: e.target.value})}
                        disabled={!isEditing}
                        placeholder="+1234567890"
                        className="w-full px-4 py-3 border border-neutral-200 rounded-2xl focus:ring-2 focus:ring-primary-500 focus:border-transparent disabled:bg-neutral-50 transition-all duration-200"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-semibold text-neutral-700 mb-2">
                      Website
                    </label>
                    <input
                      type="url"
                      value={editingCard.website}
                      onChange={(e) => setEditingCard({...editingCard, website: e.target.value})}
                      disabled={!isEditing}
                      className="w-full px-4 py-3 border border-neutral-200 rounded-2xl focus:ring-2 focus:ring-primary-500 focus:border-transparent disabled:bg-neutral-50 transition-all duration-200"
                    />
                  </div>

                  {/* Offers Title Field */}
                  <div>
                    <label className="block text-sm font-semibold text-neutral-700 mb-2">
                      Offers Section Title
                    </label>
                    <input
                      type="text"
                      value={editingCard.offersTitle || 'Special Offers'}
                      onChange={(e) => setEditingCard({...editingCard, offersTitle: e.target.value})}
                      disabled={!isEditing}
                      placeholder="Special Offers"
                      className="w-full px-4 py-3 border border-neutral-200 rounded-2xl focus:ring-2 focus:ring-primary-500 focus:border-transparent disabled:bg-neutral-50 transition-all duration-200"
                    />
                    <p className="text-xs text-neutral-500 mt-1">
                      This title will appear above your special offers section on your business card
                    </p>
                  </div>

                  {/* Social Links Editor */}
                  <div className={!isEditing ? 'opacity-50 pointer-events-none' : ''}>
                    <SocialLinksEditor
                      socialLinks={editingCard.socialLinks}
                      onSocialLinksChange={handleSocialLinksChange}
                    />
                  </div>
                </div>
              </>
            ) : (
              <>
                {/* Cover Photo Upload - Now in Design tab */}
                <div className={!isEditing ? 'opacity-50 pointer-events-none' : ''}>
                  <CoverImageUpload
                    currentImage={editingCard.coverImage}
                    onImageChange={handleCoverImageChange}
                    canUpload={hasPermission('change_background')}
                    onUpgradeClick={onUpgradeClick}
                  />
                </div>

                {/* Page Background Selector */}
                <div className={!isEditing ? 'opacity-50 pointer-events-none' : ''}>
                  <PageBackgroundSelector
                    currentBackground={editingCard.pageBackground}
                    onBackgroundChange={handlePageBackgroundChange}
                    canChangeBackground={hasPermission('change_background')}
                    onUpgradeClick={onUpgradeClick}
                  />
                </div>
              </>
            )}
          </div>
        </div>

        {/* Preview */}
        <div className="bg-white/70 backdrop-blur-sm rounded-2xl shadow-soft p-6 sm:p-8 border border-white/50">
          <div className="flex items-center mb-6 sm:mb-8">
            <Eye className="w-6 h-6 text-primary-500 mr-3" />
            <h2 className="text-xl sm:text-2xl font-bold text-neutral-900">Live Preview</h2>
          </div>
          
          {/* Preview Container with Page Background */}
          <div 
            className={`rounded-2xl p-6 sm:p-8 relative overflow-hidden ${getPageBackgroundStyle(editingCard.pageBackground)}`}
            style={getPageBackgroundImage(editingCard.pageBackground)}
          >
            {/* Page overlay for image backgrounds */}
            {getPageOverlay(editingCard.pageBackground) && (
              <div 
                className="absolute inset-0 pointer-events-none"
                style={getPageOverlay(editingCard.pageBackground)}
              />
            )}

            {/* Business Card Preview */}
            <div className="relative z-10 max-w-sm mx-auto">
              <div className="bg-white rounded-2xl shadow-large overflow-hidden border border-white/50">
                {/* Cover Photo or Theme Background */}
                {editingCard.coverImage ? (
                  <div className="h-20 sm:h-24 relative overflow-hidden">
                    <img 
                      src={editingCard.coverImage} 
                      alt="Cover"
                      className="w-full h-full object-cover"
                    />
                    <div className="absolute inset-0 bg-black bg-opacity-20"></div>
                  </div>
                ) : (
                  <div className="h-20 sm:h-24 relative overflow-hidden">
                    <div className={`w-full h-full bg-gradient-to-r ${getCardThemeGradient(editingCard.theme)} transition-all duration-500`}></div>
                    <div className="absolute inset-0 bg-black bg-opacity-20"></div>
                  </div>
                )}

                {/* Profile Picture - Positioned below the cover */}
                <div className="relative -mt-6 sm:-mt-8 flex justify-center mb-4">
                  <div className="w-12 h-12 sm:w-16 sm:h-16 rounded-full overflow-hidden border-4 border-white shadow-medium bg-white">
                    <img 
                      src={editingCard.profileImage} 
                      alt={editingCard.name}
                      className="w-full h-full object-cover"
                    />
                  </div>
                </div>

                {/* Content */}
                <div className="px-4 sm:px-6 pb-4 sm:pb-6">
                  <div className="text-center mb-4">
                    <h3 className="text-base sm:text-lg font-bold text-neutral-900 mb-1">{editingCard.name}</h3>
                    <p className="text-neutral-600 text-xs sm:text-sm font-medium">{editingCard.title}</p>
                    <p className="text-neutral-500 text-xs">{editingCard.company}</p>
                  </div>

                  <p className="text-neutral-700 text-xs leading-relaxed mb-4 sm:mb-6 text-center">
                    {editingCard.bio}
                  </p>
                  
                  <div className="flex justify-center space-x-2">
                    <div className="w-6 h-6 sm:w-8 sm:h-8 bg-gradient-to-br from-primary-400 to-primary-500 rounded-full flex items-center justify-center">
                      <div className="w-2 h-2 sm:w-3 sm:h-3 bg-white rounded-full"></div>
                    </div>
                    <div className="w-6 h-6 sm:w-8 sm:h-8 bg-gradient-to-br from-secondary-400 to-secondary-500 rounded-full flex items-center justify-center">
                      <div className="w-2 h-2 sm:w-3 sm:h-3 bg-white rounded-full"></div>
                    </div>
                    <div className="w-6 h-6 sm:w-8 sm:h-8 bg-gradient-to-br from-accent-400 to-accent-500 rounded-full flex items-center justify-center">
                      <div className="w-2 h-2 sm:w-3 sm:h-3 bg-white rounded-full"></div>
                    </div>
                  </div>

                  {/* Preview offers title */}
                  <div className="mt-4 pt-4 border-t border-gray-100">
                    <h4 className="text-xs font-bold text-gray-800 text-center">
                      ✨ {editingCard.offersTitle || 'Special Offers'}
                    </h4>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}