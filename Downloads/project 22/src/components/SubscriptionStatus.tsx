import React, { useState, useEffect } from 'react';
import { Crown, Calendar, CreditCard, AlertCircle, CheckCircle, Clock, Zap, Star, Gift } from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';
import { supabase } from '../lib/supabase';
import { getProductByPriceId } from '../stripe-config';
import { UserType } from '../types';

interface SubscriptionData {
  subscription_status: string;
  price_id: string | null;
  current_period_end: number | null;
  cancel_at_period_end: boolean;
  payment_method_brand: string | null;
  payment_method_last4: string | null;
}

export default function SubscriptionStatus() {
  const { user, userProfile } = useAuth();
  const [subscription, setSubscription] = useState<SubscriptionData | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (user) {
      loadSubscription();
    }
  }, [user]);

  const loadSubscription = async () => {
    try {
      const { data, error } = await supabase
        .from('stripe_user_subscriptions')
        .select('*')
        .maybeSingle();

      if (error) {
        console.error('Error loading subscription:', error);
      } else {
        setSubscription(data);
      }
    } catch (error) {
      console.error('Error loading subscription:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="bg-white/70 backdrop-blur-sm rounded-2xl shadow-soft p-6 border border-white/50">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="h-6 bg-gray-200 rounded w-1/2"></div>
        </div>
      </div>
    );
  }

  // Check if user has a premium account based on user profile
  const isPremiumUser = userProfile && userProfile.userType !== 'free';
  const isActiveSubscription = userProfile && userProfile.subscriptionStatus === 'active';

  if (!isPremiumUser || !isActiveSubscription) {
    return (
      <div className="bg-white/70 backdrop-blur-sm rounded-2xl shadow-soft p-6 border border-white/50">
        <div className="flex items-center">
          <Crown className="w-5 h-5 text-gray-400 mr-3" />
          <div>
            <h3 className="font-semibold text-gray-900">Free Plan</h3>
            <p className="text-sm text-gray-600">Upgrade to unlock premium features</p>
          </div>
        </div>
      </div>
    );
  }

  const getPlanConfig = (userType: UserType) => {
    switch (userType) {
      case 'super_admin':
        return {
          name: 'Super Admin',
          description: 'Full system access',
          icon: Crown,
          color: 'text-purple-600',
          bgColor: 'bg-purple-100'
        };
      case 'super_unlimited':
        return {
          name: 'Super Unlimited',
          description: 'Unlimited features + E-commerce',
          icon: Zap,
          color: 'text-yellow-600',
          bgColor: 'bg-yellow-100'
        };
      case 'unlimited_yearly':
        return {
          name: 'Unlimited (Yearly)',
          description: 'All premium features',
          icon: Star,
          color: 'text-blue-600',
          bgColor: 'bg-blue-100'
        };
      case 'unlimited_monthly':
        return {
          name: 'Unlimited (Monthly)',
          description: 'All premium features',
          icon: Star,
          color: 'text-green-600',
          bgColor: 'bg-green-100'
        };
      default:
        return {
          name: 'Premium Plan',
          description: 'Premium features',
          icon: Crown,
          color: 'text-blue-600',
          bgColor: 'bg-blue-100'
        };
    }
  };

  const planConfig = getPlanConfig(userProfile.userType);
  const IconComponent = planConfig.icon;

  // Check if there's additional Stripe subscription data
  const hasStripeData = subscription && subscription.subscription_status === 'active';
  const willCancel = subscription?.cancel_at_period_end || false;
  const isPastDue = subscription?.subscription_status === 'past_due' || false;

  const getStatusIcon = () => {
    if (isPastDue) return <AlertCircle className="w-5 h-5 text-red-500" />;
    if (willCancel) return <Clock className="w-5 h-5 text-yellow-500" />;
    return <CheckCircle className="w-5 h-5 text-green-500" />;
  };

  const getStatusText = () => {
    if (isPastDue) return 'Past Due';
    if (willCancel) return 'Canceling';
    return 'Active';
  };

  const getStatusColor = () => {
    if (isPastDue) return 'text-red-700 bg-red-100';
    if (willCancel) return 'text-yellow-700 bg-yellow-100';
    return 'text-green-700 bg-green-100';
  };

  return (
    <div className="bg-white/70 backdrop-blur-sm rounded-2xl shadow-soft p-6 border border-white/50">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center">
          <IconComponent className={`w-5 h-5 ${planConfig.color} mr-3`} />
          <div>
            <h3 className="font-semibold text-gray-900">
              {planConfig.name}
            </h3>
            <p className="text-sm text-gray-600">{planConfig.description}</p>
          </div>
        </div>
        <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor()}`}>
          {getStatusText()}
        </span>
      </div>

      {userProfile.subscriptionEndDate && (
        <div className="flex items-center text-sm text-gray-600 mb-3">
          <Calendar className="w-4 h-4 mr-2" />
          <span>
            {willCancel ? 'Expires' : 'Renews'} on{' '}
            {new Date(userProfile.subscriptionEndDate).toLocaleDateString()}
          </span>
        </div>
      )}

      {hasStripeData && subscription.payment_method_brand && subscription.payment_method_last4 && (
        <div className="flex items-center text-sm text-gray-600">
          <CreditCard className="w-4 h-4 mr-2" />
          <span>
            {subscription.payment_method_brand.toUpperCase()} ending in {subscription.payment_method_last4}
          </span>
        </div>
      )}

      {willCancel && (
        <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
          <p className="text-sm text-yellow-800">
            Your subscription will be canceled at the end of the current billing period.
            You'll continue to have access to premium features until then.
          </p>
        </div>
      )}

      {isPastDue && (
        <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
          <p className="text-sm text-red-800">
            Your payment is past due. Please update your payment method to continue using premium features.
          </p>
        </div>
      )}
    </div>
  );
}