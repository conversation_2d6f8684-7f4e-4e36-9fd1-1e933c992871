import React from 'react';
import { Zap, Heart, Github, Twitter, Linkedin, Mail, Sparkles } from 'lucide-react';

export default function Footer() {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-gradient-to-br from-neutral-900 to-neutral-800 relative overflow-hidden">
      {/* Background decoration */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-primary-500/10 rounded-full blur-3xl"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-secondary-500/10 rounded-full blur-3xl"></div>
      </div>

      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Main Footer Content */}
        <div className="py-16">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            {/* Brand Section */}
            <div className="col-span-1 md:col-span-2">
              <div className="flex items-center mb-6">
                <div className="w-12 h-12 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-2xl flex items-center justify-center mr-4">
                  <Zap className="w-6 h-6 text-white" />
                </div>
                <h3 className="text-2xl font-bold text-white">Buzzz</h3>
              </div>
              <p className="text-neutral-300 mb-8 max-w-md leading-relaxed text-lg">
                Create stunning digital business cards with integrated offers and landing pages. 
                Perfect for entrepreneurs, professionals, and businesses looking to make a lasting impression.
              </p>
              <div className="flex space-x-4">
                {[
                  { icon: Twitter, href: "https://twitter.com/BuzzzMY", color: "hover:bg-blue-500" },
                  { icon: Mail, href: "mailto:<EMAIL>", color: "hover:bg-primary-600" }
                ].map((social, index) => (
                  <a
                    key={index}
                    href={social.href}
                    target="_blank"
                    rel="noopener noreferrer"
                    className={`w-12 h-12 bg-neutral-800 rounded-2xl flex items-center justify-center ${social.color} text-neutral-300 hover:text-white transition-all duration-200 transform hover:scale-110 hover:-translate-y-1`}
                  >
                    <social.icon className="w-5 h-5" />
                  </a>
                ))}
              </div>
            </div>

            {/* Quick Links */}
            <div>
              <h4 className="text-lg font-bold text-white mb-6 flex items-center">
                <Sparkles className="w-5 h-5 mr-2 text-primary-400" />
                Quick Links
              </h4>
              <ul className="space-y-4">
                {[
                  { name: "Features", href: "#features" },
                  { name: "Templates", href: "#templates" },
                  { name: "Help Center", href: "#help" }
                ].map((link, index) => (
                  <li key={index}>
                    <a 
                      href={link.href} 
                      className="text-neutral-300 hover:text-primary-400 transition-colors duration-200 font-medium hover:translate-x-1 transform inline-block"
                    >
                      {link.name}
                    </a>
                  </li>
                ))}
              </ul>
            </div>

            {/* Support */}
            <div>
              <h4 className="text-lg font-bold text-white mb-6 flex items-center">
                <Heart className="w-5 h-5 mr-2 text-accent-400" />
                Support
              </h4>
              <ul className="space-y-4">
                {[
                  { name: "Contact Us", href: "contact" },
                  { name: "Privacy Policy", href: "privacy" },
                  { name: "Terms of Service", href: "terms" },
                  { name: "FAQ", href: "faq" }
                ].map((link, index) => (
                  <li key={index}>
                    <a 
                      href={link.href} 
                      className="text-neutral-300 hover:text-accent-400 transition-colors duration-200 font-medium hover:translate-x-1 transform inline-block"
                    >
                      {link.name}
                    </a>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-neutral-700 py-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="flex items-center text-neutral-400 mb-4 md:mb-0">
              <span>Built with</span>
              <Heart className="w-4 h-4 mx-2 text-red-400 fill-current animate-pulse" />
              <span>Bolt.new</span>
            </div>
            <div className="text-neutral-400 text-sm">
              © {currentYear} Buzzz. All rights reserved.
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}