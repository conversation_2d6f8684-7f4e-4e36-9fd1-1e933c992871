import React from 'react';
import { Link } from 'react-router-dom';
import { Zap } from 'lucide-react';
import Footer from '../components/Footer';
import { useAuth } from '../contexts/AuthContext';

export default function PrivacyPolicy() {
  const { user } = useAuth();
  return (
    <>
      <nav className="bg-white/80 backdrop-blur-md border-b border-neutral-200/50 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <Link to="/" className="flex items-center group">
              <div className="w-10 h-10 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-2xl flex items-center justify-center mr-3 group-hover:scale-105 transition-transform duration-200">
                <Zap className="w-5 h-5 text-white" />
              </div>
              <span className="text-xl font-bold bg-gradient-to-r from-primary-600 to-secondary-600 bg-clip-text text-transparent">
                Buzzz
              </span>
            </Link>
            <div>
              {user ? (
                <Link 
                  to="/dashboard" 
                  className="bg-gradient-to-r from-primary-500 to-primary-600 text-white px-4 py-2 rounded-2xl hover:from-primary-600 hover:to-primary-700 transition-all duration-200 font-medium shadow-colored transform hover:scale-105 text-sm"
                >
                  Dashboard
                </Link>
              ) : (
                <Link
                  to="/"
                  className="bg-gradient-to-r from-primary-500 to-primary-600 text-white px-4 py-2 rounded-2xl hover:from-primary-600 hover:to-primary-700 transition-all duration-200 font-medium shadow-colored transform hover:scale-105 text-sm"
                >
                  Sign In
                </Link>
              )}
            </div>
          </div>
        </div>
      </nav>
      <div className="min-h-screen bg-gradient-to-br from-primary-50 to-secondary-50 flex flex-col items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-2xl w-full bg-white/80 rounded-2xl shadow-xl p-8 md:p-12">
          <h1 className="text-3xl font-bold text-neutral-900 mb-4">Privacy Policy</h1>
          <div className="prose prose-neutral max-w-none">
            <h2>Privacy Policy for Buzzz.my</h2>
            <p><strong>Effective Date:</strong> 21 June 2025</p>
            <p>At Buzzz.my, we respect your privacy and are committed to protecting your personal information. This policy explains how we collect, use, and share information when you use our platform to create and share your digital business card or bio link page.</p>
            <h3>1. Information We Collect</h3>
            <p><strong>a. Personal Information</strong><br/>
            - Name, email address, phone number (optional)<br/>
            - Profile photo, business title, social media links, website URL, etc.<br/>
            - Any additional content you add to your digital business card or link page</p>
            <p><strong>b. Technical Information</strong><br/>
            - Browser type, IP address, device info<br/>
            - Usage data such as page views and interaction statistics</p>
            <p><strong>c. Cookies & Tracking</strong><br/>
            We use cookies and similar technologies to enhance your experience, remember preferences, and measure performance.</p>
            <h3>2. How We Use Your Information</h3>
            <ul>
              <li>- Create and display your digital business card or link page</li>
              <li>- Allow you to manage and share your profile publicly</li>
              <li>- Provide customer support and technical assistance</li>
              <li>- Send updates or marketing emails (only with consent)</li>
              <li>- Improve and secure our services</li>
            </ul>
            <h3>3. Public Visibility</h3>
            <p>You control what is shown on your public profile. Information you add (e.g., links, contact info) will be publicly accessible via your Buzzz.my page unless marked private (if such option exists).</p>
            <h3>4. Sharing of Information</h3>
            <p>We do not sell your data. We may share your information with:</p>
            <ul>
              <li>- Trusted service providers (e.g., for hosting, analytics)</li>
              <li>- Legal authorities when required by law</li>
            </ul>
            <h3>5. Data Security</h3>
            <p>We use industry-standard measures to protect your information. However, no system is fully secure — use caution when sharing personal contact details on public pages.</p>
            <h3>6. Your Rights</h3>
            <ul>
              <li>- Access, edit, or delete your personal data at any time</li>
              <li>- Request data deletion by emailing us</li>
              <li>- Opt-out of marketing communications</li>
            </ul>
            <h3>7. Third-Party Links</h3>
            <p>Your page may contain links to external sites (e.g., social media). We're not responsible for their privacy practices.</p>
            <h3>8. Children's Privacy</h3>
            <p>Buzzz.my is not intended for users under 13 years old. We do not knowingly collect information from children.</p>
            <h3>9. Changes to This Policy</h3>
            <p>We may update this Privacy Policy. If we do, we'll notify users via email or update the effective date on this page.</p>
            <h3>10. Contact Us</h3>
            <p>If you have questions or requests regarding your data, please contact us at:</p>
            <ul>
              <li>📧 Email: <a href="mailto:<EMAIL>"><EMAIL></a></li>
              <li>🌐 Website: <a href="https://buzzz.my" target="_blank" rel="noopener noreferrer">https://buzzz.my</a></li>
            </ul>
          </div>
        </div>
      </div>
      <Footer />
    </>
  );
} 