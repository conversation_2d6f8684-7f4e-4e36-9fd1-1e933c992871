import React from 'react';
import { Link } from 'react-router-dom';
import { Zap } from 'lucide-react';
import Footer from '../components/Footer';
import { useAuth } from '../contexts/AuthContext';

export default function TermsOfService() {
  const { user } = useAuth();
  return (
    <>
      <nav className="bg-white/80 backdrop-blur-md border-b border-neutral-200/50 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <Link to="/" className="flex items-center group">
              <div className="w-10 h-10 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-2xl flex items-center justify-center mr-3 group-hover:scale-105 transition-transform duration-200">
                <Zap className="w-5 h-5 text-white" />
              </div>
              <span className="text-xl font-bold bg-gradient-to-r from-primary-600 to-secondary-600 bg-clip-text text-transparent">
                Buzzz
              </span>
            </Link>
            <div>
              {user ? (
                <Link 
                  to="/dashboard" 
                  className="bg-gradient-to-r from-primary-500 to-primary-600 text-white px-4 py-2 rounded-2xl hover:from-primary-600 hover:to-primary-700 transition-all duration-200 font-medium shadow-colored transform hover:scale-105 text-sm"
                >
                  Dashboard
                </Link>
              ) : (
                <Link
                  to="/"
                  className="bg-gradient-to-r from-primary-500 to-primary-600 text-white px-4 py-2 rounded-2xl hover:from-primary-600 hover:to-primary-700 transition-all duration-200 font-medium shadow-colored transform hover:scale-105 text-sm"
                >
                  Sign In
                </Link>
              )}
            </div>
          </div>
        </div>
      </nav>
      <div className="min-h-screen bg-gradient-to-br from-primary-50 to-secondary-50 flex flex-col items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-2xl w-full bg-white/80 rounded-2xl shadow-xl p-8 md:p-12">
          <h1 className="text-3xl font-bold text-neutral-900 mb-4">Terms of Service</h1>
          <div className="prose prose-neutral max-w-none">
            <h2>Terms of Service for Buzzz.my</h2>
            <p><strong>Effective Date:</strong> 21 June 2025</p>
            <p>Welcome to Buzzz.my. By accessing or using our platform, you agree to be bound by these Terms of Service (“Terms”). Please read them carefully.</p>
            <h3>1. Overview</h3>
            <p>Buzzz.my is a platform that allows users to create and share digital business cards and personalized link pages (“Services”). These Terms apply to all users and visitors of the platform.</p>
            <h3>2. Account Registration</h3>
            <ul>
              <li>- You must be at least 13 years old to use Buzzz.my.</li>
              <li>- You are responsible for maintaining the confidentiality of your account.</li>
              <li>- You agree to provide accurate and current information when registering or updating your profile.</li>
            </ul>
            <h3>3. User Content</h3>
            <p>You retain ownership of the content you post (e.g., your profile details, images, and links), but by posting on Buzzz.my, you grant us a non-exclusive license to display, host, and distribute your content for the purpose of operating the service.</p>
            <p>You agree not to upload or link to:</p>
            <ul>
              <li>- Inappropriate, offensive, or harmful content</li>
              <li>- Illegal material or copyrighted content without permission</li>
              <li>- Malicious code or spam</li>
            </ul>
            <p>We reserve the right to remove content or suspend accounts that violate these Terms.</p>
            <h3>4. Public Profiles</h3>
            <p>By default, your profile is public and shareable via a unique Buzzz.my URL. You are responsible for the content you publish, including personal or contact information.</p>
            <h3>5. Acceptable Use</h3>
            <p>You agree to use Buzzz.my only for lawful purposes. You will not:</p>
            <ul>
              <li>- Abuse or exploit the platform</li>
              <li>- Use automated bots or scripts to access the service</li>
              <li>- Attempt to interfere with the platform's security or functionality</li>
            </ul>
            <h3>6. Termination</h3>
            <p>We may suspend or terminate your account at any time if you violate these Terms or misuse the platform. You may also delete your account at any time.</p>
            <h3>7. Disclaimers</h3>
            <p>Buzzz.my is provided "as is" without warranties of any kind. We do not guarantee uninterrupted service or that the platform will be error-free.</p>
            <p>You use Buzzz.my at your own risk.</p>
            <h3>8. Limitation of Liability</h3>
            <p>To the fullest extent permitted by law, Buzzz.my and its team shall not be liable for any direct, indirect, incidental, or consequential damages arising out of your use of the platform.</p>
            <h3>9. Changes to the Terms</h3>
            <p>We may update these Terms from time to time. Continued use of the platform after changes means you accept the new Terms. Major changes will be communicated via email or site notification.</p>
            <h3>10. Contact Us</h3>
            <p>If you have questions about these Terms, contact us at:</p>
            <ul>
              <li>📧 Email: <a href="mailto:<EMAIL>"><EMAIL></a></li>
              <li>🌐 Website: <a href="https://buzzz.my" target="_blank" rel="noopener noreferrer">https://buzzz.my</a></li>
            </ul>
          </div>
        </div>
      </div>
      <Footer />
    </>
  );
} 