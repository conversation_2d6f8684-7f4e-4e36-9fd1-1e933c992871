import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { CreditCard, Zap, Globe, Users, ArrowRight, Check, Star, Smartphone, Palette, BarChart3, Sparkles, Heart, Rocket } from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';
import AuthModal from '../components/AuthModal';
import Footer from '../components/Footer';

export default function HomePage() {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [authModalOpen, setAuthModalOpen] = useState(false);
  const [authMode, setAuthMode] = useState<'signin' | 'signup'>('signin');

  // Redirect logged-in users to dashboard
  useEffect(() => {
    if (user) {
      navigate('/dashboard');
    }
  }, [user, navigate]);

  const openAuthModal = (mode: 'signin' | 'signup') => {
    setAuthMode(mode);
    setAuthModalOpen(true);
  };

  const features = [
    {
      icon: Smartphone,
      title: 'Mobile-First Design',
      description: 'Beautiful, responsive cards that look perfect on any device',
      color: 'from-primary-500 to-primary-600'
    },
    {
      icon: Palette,
      title: 'Customizable Themes',
      description: 'Choose from stunning templates or create your own unique style',
      color: 'from-secondary-500 to-secondary-600'
    },
    {
      icon: Zap,
      title: 'Instant Sharing',
      description: 'Share your card with a simple link - no app downloads required',
      color: 'from-warning-500 to-warning-600'
    },
    {
      icon: Globe,
      title: 'Global Reach',
      description: 'Connect with anyone, anywhere in the world instantly',
      color: 'from-accent-500 to-accent-600'
    },
    {
      icon: Users,
      title: 'Team Management',
      description: 'Manage multiple cards for your entire team from one dashboard',
      color: 'from-primary-600 to-secondary-600'
    },
    {
      icon: BarChart3,
      title: 'Analytics & Insights',
      description: 'Track views, clicks, and engagement with detailed analytics',
      color: 'from-accent-600 to-primary-600'
    }
  ];

  const testimonials = [
    {
      name: 'Sarah Johnson',
      role: 'Marketing Director',
      company: 'TechCorp',
      image: 'https://images.pexels.com/photos/3785077/pexels-photo-3785077.jpeg?auto=compress&cs=tinysrgb&w=400',
      quote: 'Buzzz transformed how I network. The integrated offers feature has generated 40% more leads!',
      rating: 5
    },
    {
      name: 'Michael Chen',
      role: 'Entrepreneur',
      company: 'StartupXYZ',
      image: 'https://images.pexels.com/photos/2379004/pexels-photo-2379004.jpeg?auto=compress&cs=tinysrgb&w=400',
      quote: 'The analytics dashboard helps me understand which networking events bring the best connections.',
      rating: 5
    },
    {
      name: 'Emily Rodriguez',
      role: 'Sales Manager',
      company: 'GrowthCo',
      image: 'https://images.pexels.com/photos/3756679/pexels-photo-3756679.jpeg?auto=compress&cs=tinysrgb&w=400',
      quote: 'My team loves how easy it is to update their cards. We save hours every week!',
      rating: 5
    }
  ];

  const pricingPlans = [
    {
      name: 'Starter',
      price: 'Free',
      description: 'Perfect for individuals getting started',
      features: [
        '1 Digital Business Card',
        'Basic Templates',
        'Social Media Links',
        'Contact Information',
        'Mobile Responsive'
      ],
      cta: 'Get Started Free',
      popular: false,
      color: 'from-neutral-100 to-neutral-50'
    },
    {
      name: 'Professional',
      price: '$9',
      period: '/month',
      description: 'Ideal for professionals and freelancers',
      features: [
        '5 Digital Business Cards',
        'Premium Templates',
        'Custom Branding',
        'Special Offers Integration',
        'Basic Analytics',
        'Priority Support'
      ],
      cta: 'Start Free Trial',
      popular: true,
      color: 'from-primary-50 to-secondary-50'
    },
    {
      name: 'Business',
      price: '$29',
      period: '/month',
      description: 'Perfect for teams and growing businesses',
      features: [
        'Unlimited Business Cards',
        'Team Management',
        'Advanced Analytics',
        'Custom Domains',
        'API Access',
        'White-label Options',
        '24/7 Support'
      ],
      cta: 'Contact Sales',
      popular: false,
      color: 'from-accent-50 to-primary-50'
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-neutral-50 via-white to-primary-50">
      {/* Navigation */}
      <nav className="bg-white/80 backdrop-blur-md border-b border-neutral-200/50 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center group">
              <div className="w-10 h-10 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-2xl flex items-center justify-center mr-3 group-hover:scale-105 transition-transform duration-200">
                <Zap className="w-5 h-5 text-white" />
              </div>
              <span className="text-xl font-bold bg-gradient-to-r from-primary-600 to-secondary-600 bg-clip-text text-transparent">
                Buzzz
              </span>
            </div>
            
            <div className="hidden md:flex items-center space-x-8">
              <a href="#features" className="text-neutral-600 hover:text-primary-600 transition-colors duration-200 font-medium">Features</a>
              <a href="#testimonials" className="text-neutral-600 hover:text-primary-600 transition-colors duration-200 font-medium">Reviews</a>
              
              {user ? (
                <Link 
                  to="/dashboard" 
                  className="bg-gradient-to-r from-primary-500 to-primary-600 text-white px-6 py-2.5 rounded-2xl hover:from-primary-600 hover:to-primary-700 transition-all duration-200 font-medium shadow-colored transform hover:scale-105"
                >
                  Dashboard
                </Link>
              ) : (
                <div className="flex items-center space-x-4">
                  <button
                    onClick={() => openAuthModal('signin')}
                    className="text-neutral-600 hover:text-primary-600 transition-colors duration-200 font-medium"
                  >
                    Sign In
                  </button>
                  <button
                    onClick={() => openAuthModal('signup')}
                    className="bg-gradient-to-r from-primary-500 to-primary-600 text-white px-6 py-2.5 rounded-2xl hover:from-primary-600 hover:to-primary-700 transition-all duration-200 font-medium shadow-colored transform hover:scale-105"
                  >
                    Get Started
                  </button>
                </div>
              )}
            </div>

            {/* Mobile menu button */}
            <div className="md:hidden">
              {user ? (
                <Link 
                  to="/dashboard" 
                  className="bg-gradient-to-r from-primary-500 to-primary-600 text-white px-4 py-2 rounded-2xl hover:from-primary-600 hover:to-primary-700 transition-all duration-200 font-medium shadow-colored transform hover:scale-105 text-sm"
                >
                  Dashboard
                </Link>
              ) : (
                <button
                  onClick={() => openAuthModal('signin')}
                  className="bg-gradient-to-r from-primary-500 to-primary-600 text-white px-4 py-2 rounded-2xl hover:from-primary-600 hover:to-primary-700 transition-all duration-200 font-medium shadow-colored transform hover:scale-105 text-sm"
                >
                  Sign In
                </button>
              )}
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="relative py-20 lg:py-32 overflow-hidden">
        {/* Background decorations */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-primary-200/30 to-secondary-200/30 rounded-full blur-3xl animate-float"></div>
          <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-br from-accent-200/30 to-warning-200/30 rounded-full blur-3xl animate-float" style={{animationDelay: '1s'}}></div>
        </div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center animate-fade-in">
            <div className="inline-flex items-center bg-gradient-to-r from-primary-100 to-secondary-100 rounded-full px-6 py-2 mb-8">
              <Sparkles className="w-4 h-4 text-primary-600 mr-2" />
              <span className="text-primary-700 font-medium text-sm">✨ Transform your networking game</span>
            </div>
            
            <h1 className="text-5xl md:text-7xl font-bold text-neutral-900 mb-6 leading-tight">
              Your Digital Business Card
              <span className="block bg-gradient-to-r from-primary-600 via-secondary-600 to-accent-600 bg-clip-text text-transparent">
                Reimagined
              </span>
            </h1>
            
            <p className="text-xl md:text-2xl text-neutral-600 mb-10 max-w-4xl mx-auto leading-relaxed">
              Create stunning digital business cards with integrated offers and landing pages. 
              Share your professional identity instantly and track your networking success.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-16">
              {user ? (
                <Link 
                  to="/dashboard" 
                  className="group bg-gradient-to-r from-primary-500 to-primary-600 text-white px-8 py-4 rounded-2xl hover:from-primary-600 hover:to-primary-700 transition-all duration-200 font-semibold text-lg flex items-center justify-center shadow-colored-lg transform hover:scale-105"
                >
                  Go to Dashboard
                  <ArrowRight className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" />
                </Link>
              ) : (
                <button
                  onClick={() => openAuthModal('signup')}
                  className="group bg-gradient-to-r from-primary-500 to-primary-600 text-white px-8 py-4 rounded-2xl hover:from-primary-600 hover:to-primary-700 transition-all duration-200 font-semibold text-lg flex items-center justify-center shadow-colored-lg transform hover:scale-105"
                >
                  Create Your Card
                  <ArrowRight className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" />
                </button>
              )}
              <a 
                href="#demo" 
                className="group border-2 border-neutral-200 text-neutral-700 px-8 py-4 rounded-2xl hover:border-primary-300 hover:text-primary-600 hover:bg-primary-50 transition-all duration-200 font-semibold text-lg flex items-center justify-center"
              >
                <Rocket className="w-5 h-5 mr-2 group-hover:scale-110 transition-transform" />
                View Demo
              </a>
            </div>
          </div>
          
          {/* Hero Demo Card */}
          <div className="relative max-w-4xl mx-auto animate-slide-up">
            <div className="bg-white/70 backdrop-blur-md rounded-3xl shadow-large p-8 border border-white/50">
              <div className="grid md:grid-cols-2 gap-8 items-center">
                <div className="space-y-6">
                  <h3 className="text-2xl font-bold text-neutral-900">See it in action</h3>
                  <p className="text-neutral-600 leading-relaxed">
                    Professional, mobile-responsive, and packed with features that help you stand out from the crowd.
                  </p>
                  <div className="space-y-4">
                    {[
                      { icon: Check, text: 'Instant sharing with QR codes', color: 'text-accent-500' },
                      { icon: Check, text: 'Integrated special offers', color: 'text-primary-500' },
                      { icon: Check, text: 'Real-time analytics', color: 'text-secondary-500' }
                    ].map((item, index) => (
                      <div key={index} className="flex items-center animate-slide-in" style={{animationDelay: `${index * 0.1}s`}}>
                        <div className={`w-6 h-6 ${item.color} mr-3 flex-shrink-0`}>
                          <item.icon className="w-full h-full" />
                        </div>
                        <span className="text-neutral-700 font-medium">{item.text}</span>
                      </div>
                    ))}
                  </div>
                </div>
                
                <div className="relative">
                  <div className="bg-gradient-to-br from-primary-100 via-secondary-100 to-accent-100 rounded-2xl p-6 transform rotate-2 hover:rotate-0 transition-transform duration-300">
                    <div className="bg-white rounded-xl shadow-medium p-6 transform -rotate-1">
                      <div className="flex items-center mb-4">
                        <div className="w-12 h-12 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-full flex items-center justify-center text-white font-bold text-lg">
                          JD
                        </div>
                        <div className="ml-4">
                          <h4 className="font-bold text-neutral-900">John Doe</h4>
                          <p className="text-sm text-neutral-600">Product Manager</p>
                          <p className="text-xs text-neutral-500">TechCorp Inc.</p>
                        </div>
                      </div>
                      <p className="text-xs text-neutral-500 mb-4">Helping teams build amazing products that users love</p>
                      <div className="flex space-x-2">
                        <div className="w-8 h-8 bg-gradient-to-br from-primary-400 to-primary-500 rounded-full flex items-center justify-center">
                          <div className="w-3 h-3 bg-white rounded-full"></div>
                        </div>
                        <div className="w-8 h-8 bg-gradient-to-br from-secondary-400 to-secondary-500 rounded-full flex items-center justify-center">
                          <div className="w-3 h-3 bg-white rounded-full"></div>
                        </div>
                        <div className="w-8 h-8 bg-gradient-to-br from-accent-400 to-accent-500 rounded-full flex items-center justify-center">
                          <div className="w-3 h-3 bg-white rounded-full"></div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-20 lg:py-32 bg-gradient-to-br from-white to-neutral-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16 animate-fade-in">
            <div className="inline-flex items-center bg-gradient-to-r from-accent-100 to-primary-100 rounded-full px-6 py-2 mb-6">
              <Heart className="w-4 h-4 text-accent-600 mr-2" />
              <span className="text-accent-700 font-medium text-sm">Features you'll love</span>
            </div>
            <h2 className="text-4xl md:text-5xl font-bold text-neutral-900 mb-6">
              Everything you need to 
              <span className="block bg-gradient-to-r from-primary-600 to-accent-600 bg-clip-text text-transparent">
                network smarter
              </span>
            </h2>
            <p className="text-xl text-neutral-600 max-w-3xl mx-auto leading-relaxed">
              Powerful features designed to help you make meaningful connections and grow your professional network.
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <div 
                key={index} 
                className="group bg-white/70 backdrop-blur-sm rounded-2xl p-8 shadow-soft hover:shadow-medium transition-all duration-300 border border-white/50 hover:border-primary-200 animate-slide-up"
                style={{animationDelay: `${index * 0.1}s`}}
              >
                <div className={`w-14 h-14 bg-gradient-to-br ${feature.color} rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300`}>
                  <feature.icon className="w-7 h-7 text-white" />
                </div>
                <h3 className="text-xl font-bold text-neutral-900 mb-4 group-hover:text-primary-600 transition-colors">
                  {feature.title}
                </h3>
                <p className="text-neutral-600 leading-relaxed">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section id="testimonials" className="py-20 lg:py-32 bg-gradient-to-br from-white to-primary-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16 animate-fade-in">
            <div className="inline-flex items-center bg-gradient-to-r from-accent-100 to-secondary-100 rounded-full px-6 py-2 mb-6">
              <Star className="w-4 h-4 text-accent-600 mr-2" />
              <span className="text-accent-700 font-medium text-sm">Loved by Professionals</span>
            </div>
            <h2 className="text-4xl md:text-5xl font-bold text-neutral-900 mb-6">
              What our 
              <span className="bg-gradient-to-r from-accent-600 to-secondary-600 bg-clip-text text-transparent">
                customers say
              </span>
            </h2>
            <p className="text-xl text-neutral-600 leading-relaxed">
              Don't just take our word for it. Here's what some of our amazing customers have to say.
            </p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <div 
                key={index} 
                className="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-soft hover:shadow-medium transition-all duration-300 border border-white/50 animate-slide-up"
                style={{animationDelay: `${index * 0.1}s`}}
              >
                <div className="flex items-center mb-6">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <Star key={i} className="w-5 h-5 text-warning-400 fill-current" />
                  ))}
                </div>
                <p className="text-neutral-700 mb-6 leading-relaxed italic text-lg">
                  "{testimonial.quote}"
                </p>
                <div className="flex items-center">
                  <img 
                    src={testimonial.image} 
                    alt={testimonial.name}
                    className="w-14 h-14 rounded-full object-cover mr-4 ring-4 ring-white shadow-medium"
                  />
                  <div>
                    <h4 className="font-bold text-neutral-900">{testimonial.name}</h4>
                    <p className="text-sm text-neutral-600">{testimonial.role}</p>
                    <p className="text-xs text-neutral-500">{testimonial.company}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section id="cta" className="py-20 lg:py-32 bg-gradient-to-br from-primary-600 via-secondary-600 to-primary-700 text-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center animate-fade-in">
          <h2 className="text-4xl md:text-5xl font-bold mb-6">
            Ready to Create Your Professional Digital Card?
          </h2>
          <p className="text-xl text-primary-100 leading-relaxed mb-10">
            Join thousands of professionals who are making a lasting impression with Buzzz. Get started for free today.
          </p>
          <button
            onClick={() => openAuthModal('signup')}
            className="bg-white text-primary-600 font-bold px-10 py-5 rounded-2xl text-lg hover:bg-primary-50 transition-all duration-200 transform hover:scale-110 shadow-lg"
          >
            Get Started Free
          </button>
        </div>
      </section>

      <Footer />

      {/* Auth Modal */}
      <AuthModal 
        isOpen={authModalOpen}
        onClose={() => setAuthModalOpen(false)}
        initialMode={authMode}
      />
    </div>
  );
}