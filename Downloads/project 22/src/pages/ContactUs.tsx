import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { Zap } from 'lucide-react';
import Footer from '../components/Footer';
import { useAuth } from '../contexts/AuthContext';

export default function ContactUs() {
  const { user } = useAuth();
  const [authModalOpen, setAuthModalOpen] = useState(false);

  return (
    <>
      {/* Main Navigation Bar (matches HomePage) */}
      <nav className="bg-white/80 backdrop-blur-md border-b border-neutral-200/50 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <Link to="/" className="flex items-center group">
              <div className="w-10 h-10 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-2xl flex items-center justify-center mr-3 group-hover:scale-105 transition-transform duration-200">
                <Zap className="w-5 h-5 text-white" />
              </div>
              <span className="text-xl font-bold bg-gradient-to-r from-primary-600 to-secondary-600 bg-clip-text text-transparent">
                Buzzz
              </span>
            </Link>
            <div>
              {user ? (
                <Link 
                  to="/dashboard" 
                  className="bg-gradient-to-r from-primary-500 to-primary-600 text-white px-4 py-2 rounded-2xl hover:from-primary-600 hover:to-primary-700 transition-all duration-200 font-medium shadow-colored transform hover:scale-105 text-sm"
                >
                  Dashboard
                </Link>
              ) : (
                <Link
                  to="/"
                  className="bg-gradient-to-r from-primary-500 to-primary-600 text-white px-4 py-2 rounded-2xl hover:from-primary-600 hover:to-primary-700 transition-all duration-200 font-medium shadow-colored transform hover:scale-105 text-sm"
                >
                  Sign In
                </Link>
              )}
            </div>
          </div>
        </div>
      </nav>
      {/* Contact Page Content */}
      <div className="min-h-screen bg-gradient-to-br from-primary-50 to-secondary-50 flex flex-col items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-2xl w-full bg-white/80 rounded-2xl shadow-xl p-8 md:p-12">
          <h1 className="text-3xl font-bold text-neutral-900 mb-2">Contact Us</h1>
          <p className="text-neutral-600 mb-8">We'd love to hear from you! Fill out the form below and we'll get back to you soon.</p>
          <form
            action="https://formspree.io/f/mrbkandd"
            method="POST"
            className="space-y-6"
          >
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-neutral-700 mb-1">Name</label>
              <input
                type="text"
                name="name"
                id="name"
                required
                className="block w-full rounded-lg border border-neutral-300 px-4 py-2 focus:ring-primary-500 focus:border-primary-500 transition"
                placeholder="Your Name"
              />
            </div>
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-neutral-700 mb-1">Email</label>
              <input
                type="email"
                name="email"
                id="email"
                required
                className="block w-full rounded-lg border border-neutral-300 px-4 py-2 focus:ring-primary-500 focus:border-primary-500 transition"
                placeholder="<EMAIL>"
              />
            </div>
            <div>
              <label htmlFor="message" className="block text-sm font-medium text-neutral-700 mb-1">Message</label>
              <textarea
                name="message"
                id="message"
                rows={5}
                required
                className="block w-full rounded-lg border border-neutral-300 px-4 py-2 focus:ring-primary-500 focus:border-primary-500 transition"
                placeholder="How can we help you?"
              />
            </div>
            <button
              type="submit"
              className="w-full bg-primary-600 hover:bg-primary-700 text-white font-semibold py-2.5 rounded-lg transition"
            >
              Send Message
            </button>
          </form>
          <div className="mt-10 border-t pt-8">
            <h2 className="text-lg font-semibold text-neutral-900 mb-2">Contact Information</h2>
            <p className="text-neutral-700 mb-1">Email: <a href="mailto:<EMAIL>" className="text-primary-600 hover:underline"><EMAIL></a></p>
            <p className="text-neutral-700 mb-4">Manong, Perak, Malaysia</p>
            <div className="w-full h-48 rounded-lg overflow-hidden bg-neutral-200 flex items-center justify-center">
              <span className="text-neutral-500">[Map Placeholder]</span>
            </div>
          </div>
        </div>
      </div>
      <Footer />
    </>
  );
} 