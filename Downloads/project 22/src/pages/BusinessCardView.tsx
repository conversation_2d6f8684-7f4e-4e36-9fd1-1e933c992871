import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { ArrowLeft, Download, Bookmark, Check, Heart } from 'lucide-react';
import BusinessCard from '../components/BusinessCard';
import LandingPage from '../components/LandingPage';
import { supabase } from '../lib/supabase';
import { BusinessCard as BusinessCardType, Offer, LandingPage as LandingPageType, PageBackground } from '../types';
import { trackAnalytics } from '../utils/analytics';
import { downloadVCard } from '../utils/vcard';
import { useAuth } from '../contexts/AuthContext';

export default function BusinessCardView() {
  const { username } = useParams<{ username: string }>();
  const navigate = useNavigate();
  const { user } = useAuth();
  const [businessCard, setBusinessCard] = useState<BusinessCardType | null>(null);
  const [offers, setOffers] = useState<Offer[]>([]);
  const [selectedLandingPage, setSelectedLandingPage] = useState<LandingPageType | null>(null);
  const [loading, setLoading] = useState(true);
  const [notFound, setNotFound] = useState(false);
  const [isSaved, setIsSaved] = useState(false);
  const [saving, setSaving] = useState(false);
  const [showBackButton, setShowBackButton] = useState(false);

  useEffect(() => {
    if (username) {
      loadBusinessCard();
      // Check URL parameters immediately
      const urlParams = new URLSearchParams(window.location.search);
      const fromPreview = urlParams.get('preview');
      if (fromPreview) {
        setShowBackButton(true);
      }
    }
  }, [username]);

  useEffect(() => {
    if (businessCard && user) {
      checkIfCardIsSaved();
      checkIfFromDashboard();
    }
  }, [businessCard, user]);

  const checkIfFromDashboard = () => {
    // Check URL parameters first
    const urlParams = new URLSearchParams(window.location.search);
    const fromPreview = urlParams.get('preview');
    
    // Check referrer
    const referrer = document.referrer;
    const isFromDashboard = referrer.includes('/dashboard') || referrer.includes('localhost:5173/dashboard') || referrer.includes('localhost:3000/dashboard');
    
    // Check if user is logged in and this is their own card
    const isOwnCard = user && businessCard && businessCard.userId === user.id;
    
    // Show back button if coming from preview or dashboard
    setShowBackButton(Boolean(fromPreview) || isFromDashboard || Boolean(isOwnCard));
  };

  const loadBusinessCard = async () => {
    try {
      // Clean username - remove @ if present
      const cleanUsername = username?.replace('@', '') || '';
      
      // Load business card by username
      const { data: cardData, error } = await supabase
        .from('business_cards')
        .select('*')
        .eq('username', cleanUsername)
        .single();

      if (error || !cardData) {
        setNotFound(true);
        setLoading(false);
        return;
      }

      const card: BusinessCardType = {
        id: cardData.id,
        userId: cardData.user_id,
        name: cardData.name,
        title: cardData.title,
        company: cardData.company,
        bio: cardData.bio,
        email: cardData.email,
        phone: cardData.phone,
        website: cardData.website,
        profileImage: cardData.profile_image,
        backgroundImage: cardData.background_image,
        coverImage: cardData.cover_image,
        socialLinks: cardData.social_links || [],
        theme: cardData.theme || 'default',
        pageBackground: cardData.page_background || undefined,
        username: cardData.username,
        offersTitle: cardData.offers_title || 'Special Offers', // Load custom offers title
        location: cardData.location, // Add location
        joinYear: cardData.join_year // Add joinYear
      };
      setBusinessCard(card);

      // Track card view
      await trackAnalytics({
        businessCardId: cardData.id,
        eventType: 'card_view',
        userId: cardData.user_id
      });

      // Load offers for this card
      const { data: offersData } = await supabase
        .from('offers')
        .select('*')
        .eq('business_card_id', cardData.id)
        .eq('is_active', true)
        .order('order_index', { ascending: true });

      if (offersData) {
        const formattedOffers: Offer[] = offersData.map(offer => ({
          id: offer.id,
          title: offer.title,
          description: offer.description,
          buttonText: offer.button_text,
          landingPage: offer.landing_page,
          isActive: offer.is_active
        }));
        setOffers(formattedOffers);
      }
    } catch (error) {
      console.error('Error loading business card:', error);
      setNotFound(true);
    } finally {
      setLoading(false);
    }
  };

  const checkIfCardIsSaved = async () => {
    if (!businessCard || !user) return;

    try {
      const { data, error } = await supabase
        .from('saved_cards')
        .select('id')
        .eq('user_id', user.id)
        .eq('saved_card_data->>id', businessCard.id)
        .limit(1);

      if (data && data.length > 0 && !error) {
        setIsSaved(true);
      }
    } catch (error) {
      console.error('Error checking if card is saved:', error);
    }
  };

  const handleOfferClick = async (offer: Offer) => {
    // Immediately update the UI for instant feedback
    setSelectedLandingPage(offer.landingPage);
    
    // Track analytics in the background (non-blocking)
    if (businessCard) {
      trackAnalytics({
        businessCardId: businessCard.id,
        eventType: 'offer_click',
        eventData: { offerId: offer.id, offerTitle: offer.title },
        userId: businessCard.userId
      }).catch(error => {
        console.error('Analytics tracking failed:', error);
      });
    }
  };

  const handleContactClick = async (contactType: string) => {
    if (businessCard) {
      // Track contact click in the background (non-blocking)
      trackAnalytics({
        businessCardId: businessCard.id,
        eventType: 'contact_click',
        eventData: { contactType },
        userId: businessCard.userId
      }).catch(error => {
        console.error('Analytics tracking failed:', error);
      });
    }
  };

  const handleSocialClick = async (platform: string) => {
    if (businessCard) {
      // Track social click in the background (non-blocking)
      trackAnalytics({
        businessCardId: businessCard.id,
        eventType: 'social_click',
        eventData: { platform },
        userId: businessCard.userId
      }).catch(error => {
        console.error('Analytics tracking failed:', error);
      });
    }
  };

  const handleSaveContact = () => {
    if (businessCard) {
      downloadVCard(businessCard);
      // Track save contact action
      trackAnalytics({
        businessCardId: businessCard.id,
        eventType: 'contact_click',
        eventData: { contactType: 'save_contact' },
        userId: businessCard.userId
      });
    }
  };

  const handleSaveCard = async () => {
    if (!businessCard || !user || saving) return;

    setSaving(true);
    try {
      if (isSaved) {
        // Remove from saved cards
        const { error } = await supabase
          .from('saved_cards')
          .delete()
          .eq('user_id', user.id)
          .eq('saved_card_data->>id', businessCard.id);

        if (!error) {
          setIsSaved(false);
        }
      } else {
        // Add to saved cards
        const { error } = await supabase
          .from('saved_cards')
          .insert({
            user_id: user.id,
            saved_card_data: businessCard,
            saved_at: new Date().toISOString()
          });

        if (!error) {
          setIsSaved(true);
        }
      }

      // Track save card action
      await trackAnalytics({
        businessCardId: businessCard.id,
        eventType: 'contact_click',
        eventData: { contactType: isSaved ? 'unsave_card' : 'save_card' },
        userId: businessCard.userId
      });
    } catch (error) {
      console.error('Error saving/unsaving card:', error);
    } finally {
      setSaving(false);
    }
  };

  const handleCloseLandingPage = () => {
    setSelectedLandingPage(null);
  };

  const handleGoBack = () => {
    // Check if there's a previous page in history
    if (window.history.length > 1) {
      navigate(-1);
    } else {
      // Fallback to homepage if no history
      navigate('/');
    }
  };

  const getPageBackgroundStyle = (pageBackground?: PageBackground) => {
    if (!pageBackground) {
      return 'bg-gradient-to-br from-neutral-50 to-primary-50';
    }

    switch (pageBackground.type) {
      case 'gradient':
        return pageBackground.value;
      case 'image':
        return '';
      case 'pattern':
        return pageBackground.value;
      default:
        return 'bg-gradient-to-br from-neutral-50 to-primary-50';
    }
  };

  const getPageBackgroundImage = (pageBackground?: PageBackground) => {
    if (pageBackground?.type === 'image') {
      return {
        backgroundImage: `url(${pageBackground.value})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundAttachment: 'fixed'
      };
    }
    if (pageBackground?.type === 'pattern') {
      return {
        backgroundImage: pageBackground.value,
        backgroundColor: '#f9fafb',
        backgroundSize: '20px 20px', // default, can be improved by storing patternSize
        backgroundRepeat: 'repeat'
      };
    }
    return {};
  };

  const getPageOverlay = (pageBackground?: PageBackground) => {
    if (pageBackground?.type === 'image' && pageBackground.overlay?.enabled) {
      return {
        backgroundColor: pageBackground.overlay.color,
        opacity: pageBackground.overlay.opacity
      };
    }
    return null;
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-neutral-50 to-primary-50 flex items-center justify-center p-4">
        <div className="text-center animate-fade-in">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
          <p className="text-neutral-600">Loading business card...</p>
        </div>
      </div>
    );
  }

  if (notFound || !businessCard) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-neutral-50 to-primary-50 flex items-center justify-center p-4">
        <div className="text-center animate-fade-in">
          <h1 className="text-3xl sm:text-4xl font-bold text-neutral-900 mb-4">Card Not Found</h1>
          <p className="text-neutral-600 mb-8">The business card you're looking for doesn't exist or has been removed.</p>
          <a 
            href="/"
            className="bg-gradient-to-r from-primary-500 to-primary-600 text-white px-6 py-3 rounded-2xl hover:from-primary-600 hover:to-primary-700 transition-all duration-200 font-medium shadow-colored transform hover:scale-105"
          >
            Go to Homepage
          </a>
        </div>
      </div>
    );
  }

  const pageOverlay = getPageOverlay(businessCard.pageBackground);

  return (
    <div className="min-h-screen flex flex-col">
      {/* Main Content Area */}
      <div 
        className={`flex-1 ${getPageBackgroundStyle(businessCard.pageBackground)}`}
        style={getPageBackgroundImage(businessCard.pageBackground)}
      >
        {/* Page overlay for image backgrounds */}
        {pageOverlay && (
          <div 
            className="fixed inset-0 pointer-events-none z-0"
            style={pageOverlay}
          />
        )}

        {/* Action Buttons - Fixed position */}
        <div className="fixed top-4 left-4 right-4 z-50 flex justify-between">
          {showBackButton && (
            <button
              onClick={handleGoBack}
              className={`flex items-center bg-white/90 backdrop-blur-sm text-neutral-700 px-3 sm:px-4 py-2 rounded-full shadow-large hover:bg-white hover:shadow-xl transition-all duration-200 group ${
                selectedLandingPage ? 'hidden' : ''
              }`}
            >
              <ArrowLeft className="w-4 h-4 sm:w-5 sm:h-5 mr-1 sm:mr-2 group-hover:-translate-x-1 transition-transform duration-200" />
              <span className="font-medium text-sm sm:text-base">Back</span>
            </button>
          )}

          <div className="flex space-x-2 sm:space-x-3">
            {/* Save Card Button (only show if user is logged in and it's not their own card) */}
            {user && businessCard.userId !== user.id && (
              <button
                onClick={handleSaveCard}
                disabled={saving}
                className={`flex items-center backdrop-blur-sm text-white px-3 sm:px-4 py-2 rounded-full shadow-large hover:shadow-xl transition-all duration-200 group text-sm sm:text-base ${
                  isSaved 
                    ? 'bg-primary-600/90 hover:bg-primary-600' 
                    : 'bg-secondary-600/90 hover:bg-secondary-600'
                } ${saving ? 'opacity-50 cursor-not-allowed' : ''}`}
              >
                {isSaved ? (
                  <Check className="w-4 h-4 sm:w-5 sm:h-5 mr-1 sm:mr-2 group-hover:scale-110 transition-transform duration-200" />
                ) : (
                  <Bookmark className="w-4 h-4 sm:w-5 sm:h-5 mr-1 sm:mr-2 group-hover:scale-110 transition-transform duration-200" />
                )}
                <span className="font-medium hidden sm:inline">
                  {saving ? 'Saving...' : isSaved ? 'Saved' : 'Save Card'}
                </span>
                <span className="font-medium sm:hidden">
                  {saving ? '...' : isSaved ? 'Saved' : 'Save'}
                </span>
              </button>
            )}

            <button
              onClick={handleSaveContact}
              className={`flex items-center bg-accent-600/90 backdrop-blur-sm text-white px-3 sm:px-4 py-2 rounded-full shadow-large hover:bg-accent-600 hover:shadow-xl transition-all duration-200 group text-sm sm:text-base ${
                selectedLandingPage ? 'hidden' : ''
              }`}
            >
              <Download className="w-4 h-4 sm:w-5 sm:h-5 mr-1 sm:mr-2 group-hover:translate-y-0.5 transition-transform duration-200" />
              <span className="font-medium hidden sm:inline">Save Contact</span>
              <span className="font-medium sm:hidden">Save</span>
            </button>
          </div>
        </div>

        <main className="container mx-auto px-4 py-8 relative z-10">
          <div className="max-w-7xl mx-auto flex flex-col lg:flex-row gap-8">
            {/* Business Card with Integrated Offers - Default Center, Move Left on Offer Click */}
            <div className={`transition-all duration-500 ease-out ${
              selectedLandingPage 
                ? 'lg:w-1/2 lg:pr-4' 
                : 'w-full flex justify-center'
            }`}>
              <div className={`${selectedLandingPage ? 'w-full animate-slide-in-left' : 'w-auto'}`}> 
                <BusinessCard 
                  card={businessCard} 
                  offers={offers}
                  onOfferClick={handleOfferClick}
                  onContactClick={handleContactClick}
                  onSocialClick={handleSocialClick}
                  isCompact={!!selectedLandingPage}
                  offersTitle={businessCard.offersTitle} // Pass the custom offers title
                />
              </div>
            </div>

            {/* Landing Page - Show as block, not absolute */}
            {selectedLandingPage && (
              <div className="lg:w-1/2 animate-slide-in-right">
                <div className="bg-white rounded-2xl shadow-xl ml-0 lg:ml-4 my-8">
                  <LandingPage 
                    landingPage={selectedLandingPage}
                    onClose={handleCloseLandingPage}
                  />
                </div>
              </div>
            )}
          </div>

          {/* Mobile Landing Page Overlay */}
          {selectedLandingPage && (
            <div className="lg:hidden fixed inset-0 z-50 bg-white animate-fade-in">
              <div className="h-full flex flex-col safe-area-top safe-area-bottom">
                {/* Landing page content with full height and proper safe areas */}
                <div className="flex-1 overflow-y-auto mobile-cta-container">
                  <LandingPage
                    landingPage={selectedLandingPage}
                    onClose={handleCloseLandingPage}
                  />
                </div>
              </div>
            </div>
          )}
        </main>
      </div>

      {/* Simple Footer - Just the bottom line */}
      <footer className="relative z-10 py-4 bg-neutral-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="flex items-center text-neutral-400 mb-4 md:mb-0">
              <span>Made with</span>
              <Heart className="w-4 h-4 mx-2 text-red-400 fill-current animate-pulse" />
              <span>by the Buzzz team</span>
            </div>
            <div className="text-neutral-400 text-sm">
              © 2025 Buzzz. All rights reserved.
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}