import React from 'react';
import { Link } from 'react-router-dom';
import { Zap } from 'lucide-react';
import Footer from '../components/Footer';
import { useAuth } from '../contexts/AuthContext';

export default function FAQ() {
  const { user } = useAuth();
  return (
    <>
      <nav className="bg-white/80 backdrop-blur-md border-b border-neutral-200/50 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <Link to="/" className="flex items-center group">
              <div className="w-10 h-10 bg-gradient-to-br from-primary-500 to-secondary-500 rounded-2xl flex items-center justify-center mr-3 group-hover:scale-105 transition-transform duration-200">
                <Zap className="w-5 h-5 text-white" />
              </div>
              <span className="text-xl font-bold bg-gradient-to-r from-primary-600 to-secondary-600 bg-clip-text text-transparent">
                Buzzz
              </span>
            </Link>
            <div>
              {user ? (
                <Link 
                  to="/dashboard" 
                  className="bg-gradient-to-r from-primary-500 to-primary-600 text-white px-4 py-2 rounded-2xl hover:from-primary-600 hover:to-primary-700 transition-all duration-200 font-medium shadow-colored transform hover:scale-105 text-sm"
                >
                  Dashboard
                </Link>
              ) : (
                <Link
                  to="/"
                  className="bg-gradient-to-r from-primary-500 to-primary-600 text-white px-4 py-2 rounded-2xl hover:from-primary-600 hover:to-primary-700 transition-all duration-200 font-medium shadow-colored transform hover:scale-105 text-sm"
                >
                  Sign In
                </Link>
              )}
            </div>
          </div>
        </div>
      </nav>
      <div className="min-h-screen bg-gradient-to-br from-primary-50 to-secondary-50 flex flex-col items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-2xl w-full bg-white/80 rounded-2xl shadow-xl p-8 md:p-12">
          <h1 className="text-3xl font-bold text-neutral-900 mb-4">Frequently Asked Questions</h1>
          <div className="prose prose-neutral max-w-none">
            <h2>What is Buzzz?</h2>
            <p>Buzzz is a platform for creating stunning digital business cards with integrated offers and landing pages.</p>
            <h2>How do I upgrade my plan?</h2>
            <p>You can upgrade your plan from your dashboard by clicking the "Upgrade" button and following the checkout process.</p>
            <h2>Can I use Buzzz for free?</h2>
            <p>Yes! Buzzz offers a free plan with essential features. You can upgrade anytime for more advanced options.</p>
            <h2>How do I contact support?</h2>
            <p>You can reach our support team via the <Link to="/contact" className="text-primary-600 hover:underline">Contact Us</Link> page or email us at <a href="mailto:<EMAIL>"><EMAIL></a>.</p>
            <h2>Is my data secure?</h2>
            <p>We take data security seriously. Please see our <Link to="/privacy" className="text-primary-600 hover:underline">Privacy Policy</Link> for more details.</p>
          </div>
        </div>
      </div>
      <Footer />
    </>
  );
} 